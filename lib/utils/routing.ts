import { SupabaseClient } from '@supabase/supabase-js'

/**
 * Determines where to redirect users based on their activity
 * New users with no follows → Dashboard (to encourage content creation)
 * Users with follows → Timeline (to see content from people they follow)
 */
export async function getOptimalUserDestination(
  supabase: SupabaseClient,
  userId: string
): Promise<'/dashboard' | '/timeline'> {
  try {
    // Check if user is following anyone
    const { data: follows, error } = await supabase
      .from('follows')
      .select('id')
      .eq('follower_id', userId)
      .limit(1)

    if (error) {
      console.error('Error checking follows:', error)
      // Default to dashboard on error
      return '/dashboard'
    }

    // If user isn't following anyone, send to dashboard to encourage content creation
    if (!follows || follows.length === 0) {
      return '/dashboard'
    }

    // If user is following people, send to timeline to see their content
    return '/timeline'
  } catch (error) {
    console.error('Error determining user destination:', error)
    return '/dashboard'
  }
}

/**
 * Checks if a user qualifies as a "creator" based on activity, not role
 */
export async function isActiveCreator(
  supabase: SupabaseClient,
  userId: string
): Promise<boolean> {
  try {
    const { data: entries, error } = await supabase
      .from('diary_entries')
      .select('id')
      .eq('user_id', userId)
      .limit(1)

    if (error) {
      console.error('Error checking creator status:', error)
      return false
    }

    return (entries && entries.length > 0) || false
  } catch (error) {
    console.error('Error checking creator status:', error)
    return false
  }
}
