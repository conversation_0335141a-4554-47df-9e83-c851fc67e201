'use client'

import { useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

/**
 * Universal hook for actions that should happen after publishing any content
 * (diary entries, books, voice posts, etc.)
 */
export function usePostPublishActions() {
  const supabase = createSupabaseClient()

  const triggerFirstPostModal = useCallback(async (userId: string) => {
    if (!userId) return

    try {
      // Check if this might be their first post and if they should see the modal
      const { data: user, error } = await supabase
        .from('users')
        .select('invite_modal_shown_first_login, invite_modal_shown_first_post')
        .eq('id', userId)
        .single()

      if (error || !user) {
        console.error('Error checking first post modal status:', error)
        return
      }

      // Trigger the modal check if conditions are met
      if (user.invite_modal_shown_first_login && !user.invite_modal_shown_first_post) {
        // Dispatch a custom event that the FirstPostModal can listen for
        window.dispatchEvent(new CustomEvent('checkFirstPostModal', { 
          detail: { userId } 
        }))
      }
    } catch (error) {
      console.error('Error triggering first post modal:', error)
    }
  }, [supabase])

  return {
    triggerFirstPostModal
  }
}
