'use client'

import { useState, useCallback } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

export function useFirstPostModal(userId: string, userName?: string) {
  const [showModal, setShowModal] = useState(false)
  const supabase = createSupabaseClient()

  const checkAndShowFirstPostModal = useCallback(async () => {
    if (!userId) return

    try {
      // Check if user should see first post modal
      const { data: user, error } = await supabase
        .from('users')
        .select('invite_modal_shown_first_login, invite_modal_shown_first_post, entry_count')
        .eq('id', userId)
        .single()

      if (error || !user) {
        console.error('Error checking first post modal status:', error)
        return
      }

      // Show modal if:
      // 1. First login modal was shown (user ignored it)
      // 2. First post modal hasn't been shown yet
      // 3. This might be their first piece of content
      const shouldShow = user.invite_modal_shown_first_login && 
                        !user.invite_modal_shown_first_post

      if (shouldShow) {
        // Small delay for better UX
        setTimeout(() => {
          setShowModal(true)
        }, 1000)
      }
    } catch (error) {
      console.error('Error checking first post modal status:', error)
    }
  }, [userId, supabase])

  const handleModalClose = useCallback(async () => {
    setShowModal(false)
    
    // Mark first post modal as shown
    try {
      await supabase
        .from('users')
        .update({ invite_modal_shown_first_post: true })
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating first post modal status:', error)
    }
  }, [userId, supabase])

  const handleInviteAction = useCallback(async () => {
    // User took action - mark both modals as complete
    try {
      await supabase
        .from('users')
        .update({
          invite_modal_shown_first_login: true,
          invite_modal_shown_first_post: true
        })
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating invite modal status:', error)
    }
    
    setShowModal(false)
  }, [userId, supabase])

  return {
    showModal,
    checkAndShowFirstPostModal,
    handleModalClose,
    handleInviteAction
  }
}
