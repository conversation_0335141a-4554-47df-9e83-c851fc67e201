-- Update Existing Users with Tiered Badge System
-- Run this AFTER running the main day1_badge_migration.sql

-- 1. Update existing users with badge tiers based on their signup numbers
UPDATE users 
SET badge_tier = CASE 
    WHEN signup_number <= 500 THEN 'day1'
    WHEN signup_number <= 1000 THEN 'pioneer'
    WHEN signup_number <= 2500 THEN 'founder'
    WH<PERSON> signup_number <= 5000 THEN 'early_adopter'
    WHEN signup_number <= 10000 THEN 'charter_member'
    ELSE NULL
END
WHERE has_day1_badge = TRUE AND badge_tier IS NULL;

-- 2. Verify the update worked
SELECT 
    badge_tier,
    COUNT(*) as count,
    MIN(signup_number) as min_signup,
    MAX(signup_number) as max_signup
FROM users 
WHERE has_day1_badge = TRUE 
GROUP BY badge_tier 
ORDER BY MIN(signup_number);

-- 3. Check <PERSON> specifically
SELECT 
    name,
    email,
    signup_number,
    has_day1_badge,
    badge_tier
FROM users 
WHERE name = '<PERSON>';

-- 4. Get badge statistics
SELECT * FROM get_badge_statistics();

-- 5. Test the new tiered badge system with a test user
INSERT INTO users (email, name) VALUES ('<EMAIL>', 'Badge Test User');

-- Check what badge the test user got
SELECT 
    name,
    email,
    signup_number,
    has_day1_badge,
    badge_tier
FROM users 
WHERE email = '<EMAIL>';

-- Clean up test user
DELETE FROM users WHERE email = '<EMAIL>';
