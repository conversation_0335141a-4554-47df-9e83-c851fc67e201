-- <PERSON><PERSON><PERSON> SYSTEM ADMIN QUERIES
-- Use these to monitor and manage your tiered badge system

-- 1. BADGE STATISTICS OVERVIEW
-- Shows count and progress for each tier
SELECT * FROM get_badge_statistics();

-- 2. CURRENT BADGE DISTRIBUTION
-- Quick overview of all badge holders
SELECT 
    badge_tier,
    COUNT(*) as holders,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE has_day1_badge = TRUE), 2) as percentage
FROM users 
WHERE has_day1_badge = TRUE 
GROUP BY badge_tier 
ORDER BY MIN(signup_number);

-- 3. RECENT BADGE HOLDERS
-- See the most recent people who got badges
SELECT 
    name,
    email,
    signup_number,
    badge_tier,
    created_at
FROM users 
WHERE has_day1_badge = TRUE 
ORDER BY created_at DESC 
LIMIT 20;

-- 4. BADGE TIER PROGRESS
-- See how close you are to filling each tier
SELECT 
    badge_tier,
    COUNT(*) as current_holders,
    CASE badge_tier
        WHEN 'day1' THEN 500
        WHEN 'pioneer' THEN 500
        WHEN 'founder' THEN 1500
        WHEN 'early_adopter' THEN 2500
        WHEN 'charter_member' THEN 5000
    END as max_possible,
    ROUND(COUNT(*) * 100.0 / CASE badge_tier
        WHEN 'day1' THEN 500
        WHEN 'pioneer' THEN 500
        WHEN 'founder' THEN 1500
        WHEN 'early_adopter' THEN 2500
        WHEN 'charter_member' THEN 5000
    END, 2) as percent_filled
FROM users 
WHERE has_day1_badge = TRUE 
GROUP BY badge_tier 
ORDER BY MIN(signup_number);

-- 5. NEXT MILESTONE COUNTDOWN
-- See how many signups until next tier milestone
SELECT 
    COUNT(*) as total_users,
    MAX(signup_number) as highest_signup_number,
    CASE 
        WHEN MAX(signup_number) < 500 THEN 500 - MAX(signup_number)
        WHEN MAX(signup_number) < 1000 THEN 1000 - MAX(signup_number)
        WHEN MAX(signup_number) < 2500 THEN 2500 - MAX(signup_number)
        WHEN MAX(signup_number) < 5000 THEN 5000 - MAX(signup_number)
        WHEN MAX(signup_number) < 10000 THEN 10000 - MAX(signup_number)
        ELSE 0
    END as signups_until_next_milestone,
    CASE 
        WHEN MAX(signup_number) < 500 THEN 'Day 1 tier fills up'
        WHEN MAX(signup_number) < 1000 THEN 'Pioneer tier fills up'
        WHEN MAX(signup_number) < 2500 THEN 'Founder tier fills up'
        WHEN MAX(signup_number) < 5000 THEN 'Early Adopter tier fills up'
        WHEN MAX(signup_number) < 10000 THEN 'Charter Member tier fills up'
        ELSE 'Badge system complete (10K reached)'
    END as next_milestone
FROM users;

-- 6. SPECIFIC TIER HOLDERS
-- Get all holders of a specific tier (change 'day1' to any tier)
SELECT * FROM get_badge_holders_by_tier('day1');

-- 7. BADGE SYSTEM HEALTH CHECK
-- Verify the system is working correctly
SELECT 
    'Total Users' as metric,
    COUNT(*)::text as value
FROM users
UNION ALL
SELECT 
    'Badge Holders' as metric,
    COUNT(*)::text as value
FROM users WHERE has_day1_badge = TRUE
UNION ALL
SELECT 
    'Users Without Badges' as metric,
    COUNT(*)::text as value
FROM users WHERE has_day1_badge = FALSE OR has_day1_badge IS NULL
UNION ALL
SELECT 
    'Highest Signup Number' as metric,
    MAX(signup_number)::text as value
FROM users
UNION ALL
SELECT 
    'Badge System Status' as metric,
    CASE 
        WHEN MAX(signup_number) >= 10000 THEN 'COMPLETE - Ready for tokenization'
        ELSE 'ACTIVE - Accepting new badge holders'
    END as value
FROM users;

-- 8. FIND SPECIFIC USER'S BADGE STATUS
-- Replace 'David Weaver' with any user name
SELECT 
    name,
    email,
    signup_number,
    has_day1_badge,
    badge_tier,
    CASE badge_tier
        WHEN 'day1' THEN 'Day 1 (1-500)'
        WHEN 'pioneer' THEN 'Pioneer (501-1,000)'
        WHEN 'founder' THEN 'Founder (1,001-2,500)'
        WHEN 'early_adopter' THEN 'Early Adopter (2,501-5,000)'
        WHEN 'charter_member' THEN 'Charter Member (5,001-10,000)'
        ELSE 'No Badge'
    END as badge_description,
    created_at
FROM users 
WHERE name ILIKE '%David Weaver%';
