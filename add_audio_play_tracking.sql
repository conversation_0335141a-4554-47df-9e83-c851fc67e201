-- Add play tracking to OnlyAudio feature
-- Run this in Supabase SQL Editor

-- 1. Add play_count column to audio_posts
ALTER TABLE audio_posts ADD COLUMN play_count INTEGER DEFAULT 0;

-- 2. Create audio_plays table to track individual plays
CREATE TABLE audio_plays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audio_post_id UUID REFERENCES audio_posts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- NULL for anonymous plays
    ip_address INET, -- For anonymous play tracking
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Prevent duplicate plays from same user/IP within short timeframe
    UNIQUE(audio_post_id, user_id, created_at::date),
    UNIQUE(audio_post_id, ip_address, created_at::date)
);

-- 3. Create indexes for performance
CREATE INDEX idx_audio_plays_post_id ON audio_plays(audio_post_id);
CREATE INDEX idx_audio_plays_user_id ON audio_plays(user_id);
CREATE INDEX idx_audio_plays_created_at ON audio_plays(created_at DESC);
CREATE INDEX idx_audio_plays_ip ON audio_plays(ip_address);

-- 4. Create function to update play count
CREATE OR REPLACE FUNCTION update_audio_play_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE audio_posts
        SET play_count = play_count + 1
        WHERE id = NEW.audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE audio_posts
        SET play_count = GREATEST(play_count - 1, 0)
        WHERE id = OLD.audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger to automatically update play counts
CREATE TRIGGER update_audio_play_count_trigger
    AFTER INSERT OR DELETE ON audio_plays
    FOR EACH ROW EXECUTE FUNCTION update_audio_play_count();

-- 6. Add achievement plaques table
CREATE TABLE audio_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audio_post_id UUID REFERENCES audio_posts(id) ON DELETE CASCADE NOT NULL,
    achievement_type VARCHAR(20) NOT NULL, -- 'gold', 'platinum', 'double_platinum', 'diamond'
    play_count_achieved INTEGER NOT NULL,
    achieved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(audio_post_id, achievement_type)
);

-- 7. Create function to award achievements
CREATE OR REPLACE FUNCTION check_audio_achievements()
RETURNS TRIGGER AS $$
DECLARE
    current_play_count INTEGER;
BEGIN
    -- Get current play count
    SELECT play_count INTO current_play_count
    FROM audio_posts
    WHERE id = NEW.audio_post_id;
    
    -- Award achievements based on play count
    -- Gold plaque at 500k plays
    IF current_play_count >= 500000 THEN
        INSERT INTO audio_achievements (audio_post_id, achievement_type, play_count_achieved)
        VALUES (NEW.audio_post_id, 'gold', current_play_count)
        ON CONFLICT (audio_post_id, achievement_type) DO NOTHING;
    END IF;
    
    -- Platinum plaque at 1M plays
    IF current_play_count >= 1000000 THEN
        INSERT INTO audio_achievements (audio_post_id, achievement_type, play_count_achieved)
        VALUES (NEW.audio_post_id, 'platinum', current_play_count)
        ON CONFLICT (audio_post_id, achievement_type) DO NOTHING;
    END IF;
    
    -- Double Platinum at 2M plays
    IF current_play_count >= 2000000 THEN
        INSERT INTO audio_achievements (audio_post_id, achievement_type, play_count_achieved)
        VALUES (NEW.audio_post_id, 'double_platinum', current_play_count)
        ON CONFLICT (audio_post_id, achievement_type) DO NOTHING;
    END IF;
    
    -- Diamond at 10M plays
    IF current_play_count >= 10000000 THEN
        INSERT INTO audio_achievements (audio_post_id, achievement_type, play_count_achieved)
        VALUES (NEW.audio_post_id, 'diamond', current_play_count)
        ON CONFLICT (audio_post_id, achievement_type) DO NOTHING;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Create trigger to check achievements on play count updates
CREATE TRIGGER check_audio_achievements_trigger
    AFTER INSERT ON audio_plays
    FOR EACH ROW EXECUTE FUNCTION check_audio_achievements();

-- 9. Create indexes for achievements
CREATE INDEX idx_audio_achievements_post_id ON audio_achievements(audio_post_id);
CREATE INDEX idx_audio_achievements_type ON audio_achievements(achievement_type);
CREATE INDEX idx_audio_achievements_achieved_at ON audio_achievements(achieved_at DESC);

-- 10. Enable RLS for new tables
ALTER TABLE audio_plays ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_achievements ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for audio_plays
CREATE POLICY "Audio plays are viewable by everyone" ON audio_plays
    FOR SELECT USING (true);

CREATE POLICY "Users can create audio plays" ON audio_plays
    FOR INSERT WITH CHECK (true); -- Allow anonymous plays

-- 12. Create RLS policies for audio_achievements
CREATE POLICY "Audio achievements are viewable by everyone" ON audio_achievements
    FOR SELECT USING (true);

-- Success message
SELECT 'Audio play tracking and achievements added successfully!' as status;
