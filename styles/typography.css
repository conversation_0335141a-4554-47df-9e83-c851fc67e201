/* Smart Typography Styles for OnlyDiary */

/* Optimal reading experience */
.smart-typography {
  /* Perfect line length for readability */
  max-width: 70ch;

  /* Optimal line height */
  line-height: 1.7;

  /* Enhanced text rendering */
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Hyphenation for better text flow */
  hyphens: auto;
  word-wrap: break-word;
}

/* Desktop-specific enhancements */
@media (min-width: 1024px) {
  .smart-typography.desktop-enhanced {
    /* Advanced typography features */
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "swsh" 1;
    font-variant-ligatures: common-ligatures contextual;

    /* Enhanced spacing */
    line-height: 1.8;
    font-size: 1.125rem;

    /* Subtle text shadow for depth */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    /* Better paragraph spacing */
    margin-bottom: 1.5rem;
  }

  .smart-typography.desktop-enhanced p {
    /* Hover effects for reading focus */
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 0.25rem 0;
  }

  .smart-typography.desktop-enhanced p:hover {
    background-color: rgba(59, 130, 246, 0.05);
    padding: 0.5rem;
    margin: 0 -0.5rem 1rem -0.5rem;
    transform: translateX(0.25rem);
  }

  /* Large screen optimizations */
  @media (min-width: 1440px) {
    .smart-typography.desktop-enhanced {
      font-size: 1.25rem;
      line-height: 2;
      max-width: 75ch;
    }
  }

  /* Ultra-wide screen optimizations */
  @media (min-width: 1920px) {
    .smart-typography.desktop-enhanced {
      font-size: 1.375rem;
      line-height: 2.2;
      max-width: 80ch;
    }
  }
}

/* Zen mode typography */
.smart-typography.zen-mode {
  max-width: 65ch;
  line-height: 1.8;
  font-size: 1.125rem;
}

/* Paragraph spacing optimization */
.smart-typography p {
  margin-bottom: 1rem;

  /* Prevent orphans and widows */
  orphans: 2;
  widows: 2;

  /* Text justification for clean edges */
  text-align: justify;
  text-justify: inter-word;
}

.smart-typography p:last-child {
  margin-bottom: 0;
}

/* Short paragraphs get tighter spacing */
.smart-typography p.short {
  margin-bottom: 0.75rem;
}

/* Long paragraphs get more breathing room */
.smart-typography p.long {
  margin-bottom: 1.5rem;
}

/* Quote styling */
.smart-typography p.quote {
  font-style: italic;
  padding-left: 1rem;
  border-left: 2px solid #d1d5db;
  margin-bottom: 1.25rem;
  color: #6b7280;
}

/* Dialogue styling */
.smart-typography p.dialogue {
  color: #374151;
}

/* List items */
.smart-typography p.list-item {
  margin-bottom: 0.5rem;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .smart-typography {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.6;
    max-width: 100%;
  }
  
  .smart-typography.zen-mode {
    font-size: 18px;
    line-height: 1.7;
  }
}

/* Flow state enhancements */
.smart-typography.flow-state {
  line-height: 2;
  letter-spacing: 0.01em;
}

.smart-typography.flow-state p {
  margin-bottom: 2rem;
}

/* Reading focus mode */
.smart-typography.focus-mode {
  color: #1f2937;
}

.smart-typography.focus-mode p {
  transition: opacity 0.3s ease;
}

.smart-typography.focus-mode p:not(:hover) {
  opacity: 0.7;
}

/* Print styles for beautiful printing */
@media print {
  .smart-typography {
    font-size: 12pt;
    line-height: 1.5;
    color: black;
  }
  
  .smart-typography p {
    margin-bottom: 12pt;
    page-break-inside: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .smart-typography {
    color: black;
  }
  
  .smart-typography p.quote {
    border-left-color: black;
    color: black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .smart-typography * {
    transition: none !important;
    animation: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .smart-typography {
    color: #f9fafb;
  }
  
  .smart-typography p.quote {
    border-left-color: #6b7280;
    color: #d1d5db;
  }
  
  .smart-typography p.dialogue {
    color: #e5e7eb;
  }
}
