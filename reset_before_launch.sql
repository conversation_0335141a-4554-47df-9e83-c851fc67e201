-- RESET SCRIPT FOR LAUNCH
-- Run this in Supabase SQL Editor to clean up ALL accounts except <PERSON>
-- WARNING: This will DELETE all users except <PERSON> (<EMAIL>)

-- 1. First, let's see what accounts exist before reset
SELECT
    name,
    email,
    created_at,
    has_day1_badge,
    signup_number
FROM users
ORDER BY created_at ASC;

-- 2. DANGER ZONE: This will delete ALL users except <PERSON>
-- Uncomment the line below when you're ready to do the full reset
-- SELECT reset_for_launch();

-- 3. Check the results after reset
SELECT
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE has_day1_badge = TRUE) as day1_badge_holders,
    <PERSON><PERSON>(signup_number) as first_signup_number,
    MAX(signup_number) as last_signup_number
FROM users;

-- 4. Verify <PERSON> is the only remaining user with signup #1 and Day 1 badge
SELECT
    name,
    email,
    signup_number,
    has_day1_badge,
    created_at
FROM users;

-- 5. Should only show <PERSON> after cleanup
SELECT COUNT(*) as total_remaining_users FROM users;
