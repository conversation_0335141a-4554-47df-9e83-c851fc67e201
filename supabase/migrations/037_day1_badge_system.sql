-- Day 1 Badge System Migration
-- Creates automatic Day 1 badge assignment for first 500 signups
-- Run this BEFORE launching to creators

-- 1. Add Day 1 badge fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS has_day1_badge BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS signup_number INTEGER;

-- 2. <PERSON><PERSON> function to automatically assign Day 1 badges to first 500 signups
CREATE OR REPLACE FUNCTION assign_day1_badge()
RETURNS TRIGGER AS $$
DECLARE
    current_signup_count INTEGER;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM users WHERE id != NEW.id;
    
    -- Assign signup number (1-based)
    NEW.signup_number := current_signup_count + 1;
    
    -- Assign Day 1 badge if within first 500 signups
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
    ELSE
        NEW.has_day1_badge := FALSE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. <PERSON>reate trigger to run the function on user signup
DROP TRIGGER IF EXISTS assign_day1_badge_trigger ON users;
CREATE TRIGGER assign_day1_badge_trigger
    BEFORE INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION assign_day1_badge();

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_signup_number ON users(signup_number);
CREATE INDEX IF NOT EXISTS idx_users_day1_badge ON users(has_day1_badge) WHERE has_day1_badge = TRUE;

-- 5. Create function to get Day 1 badge holders (for admin/analytics)
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.created_at
    FROM users u
    WHERE u.has_day1_badge = TRUE
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to get Day 1 badge count
CREATE OR REPLACE FUNCTION get_day1_badge_count()
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM users WHERE has_day1_badge = TRUE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Grant permissions
GRANT EXECUTE ON FUNCTION get_day1_badge_holders() TO authenticated;
GRANT EXECUTE ON FUNCTION get_day1_badge_count() TO authenticated;

-- 8. Update RLS policies to include new fields in user profiles
-- Users can see Day 1 badges in public profiles
DROP POLICY IF EXISTS "Users can view public profiles" ON users;
CREATE POLICY "Users can view public profiles" ON users
    FOR SELECT USING (true);

-- 9. Create function to reset signup count and badges (USE CAREFULLY - TESTING ONLY)
CREATE OR REPLACE FUNCTION reset_signup_count_and_badges()
RETURNS void AS $$
BEGIN
    -- WARNING: This will reset all signup numbers and Day 1 badges
    -- Only use this before your official launch to clear test data
    
    -- First, reset all badges and numbers
    UPDATE users SET 
        signup_number = NULL,
        has_day1_badge = FALSE;
    
    -- Then reassign based on created_at order
    WITH numbered_users AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at ASC) as new_signup_number
        FROM users
    )
    UPDATE users 
    SET 
        signup_number = numbered_users.new_signup_number,
        has_day1_badge = CASE WHEN numbered_users.new_signup_number <= 500 THEN TRUE ELSE FALSE END
    FROM numbered_users 
    WHERE users.id = numbered_users.id;
    
    -- Log the reset
    INSERT INTO settings (key, value) VALUES 
        ('last_badge_reset', to_jsonb(NOW()::text))
    ON CONFLICT (key) DO UPDATE SET 
        value = to_jsonb(NOW()::text),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Grant permission for reset function (be careful with this)
GRANT EXECUTE ON FUNCTION reset_signup_count_and_badges() TO authenticated;

-- 11. Add comment to document the system
COMMENT ON COLUMN users.has_day1_badge IS 'Permanent badge for first 500 signups - never expires';
COMMENT ON COLUMN users.signup_number IS 'Sequential signup number starting from 1';
COMMENT ON FUNCTION assign_day1_badge() IS 'Automatically assigns Day 1 badges to first 500 signups';
COMMENT ON FUNCTION reset_signup_count_and_badges() IS 'DANGER: Resets all badges and signup numbers - use only for testing';
