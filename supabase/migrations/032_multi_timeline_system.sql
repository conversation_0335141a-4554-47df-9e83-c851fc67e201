-- Multi-Timeline System for OnlyDiary
-- Creates unified content system for Diary, Books, Voices, etc.

-- 1. Create content_types enum
CREATE TYPE content_type AS ENUM ('diary', 'book', 'voice', 'podcast', 'music', 'movie');

-- 2. Create unified timeline_posts table
CREATE TABLE timeline_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content_type content_type NOT NULL,
    
    -- Universal fields
    title TEXT NOT NULL,
    description TEXT, -- Short description/excerpt
    is_free BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    
    -- Engagement metrics
    love_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    
    -- Content-specific references
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    voice_post_id UUID, -- Will reference voice_posts table when created
    
    -- Metadata
    tags TEXT[],
    featured BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure only one content reference per post
    CONSTRAINT single_content_reference CHECK (
        (diary_entry_id IS NOT NULL)::integer + 
        (project_id IS NOT NULL)::integer + 
        (voice_post_id IS NOT NULL)::integer = 1
    )
);

-- 3. Create voice_posts table for OnlyVoices
CREATE TABLE voice_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    duration_seconds INTEGER NOT NULL,
    file_size_bytes INTEGER,
    waveform_data JSONB, -- For visual waveform display
    
    -- Monetization
    is_free BOOLEAN DEFAULT FALSE,
    
    -- Engagement
    play_count INTEGER DEFAULT 0,
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create indexes for performance
CREATE INDEX idx_timeline_posts_user_id ON timeline_posts(user_id);
CREATE INDEX idx_timeline_posts_content_type ON timeline_posts(content_type);
CREATE INDEX idx_timeline_posts_created_at ON timeline_posts(created_at DESC);
CREATE INDEX idx_timeline_posts_featured ON timeline_posts(featured) WHERE featured = true;
CREATE INDEX idx_timeline_posts_composite ON timeline_posts(content_type, created_at DESC, is_hidden);

CREATE INDEX idx_voice_posts_user_id ON voice_posts(user_id);
CREATE INDEX idx_voice_posts_created_at ON voice_posts(created_at DESC);

-- 5. Enable RLS
ALTER TABLE timeline_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_posts ENABLE ROW LEVEL SECURITY;

-- 6. RLS Policies for timeline_posts
CREATE POLICY "Anyone can view non-hidden timeline posts" ON timeline_posts
    FOR SELECT USING (NOT is_hidden);

CREATE POLICY "Users can manage their own timeline posts" ON timeline_posts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all timeline posts" ON timeline_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 7. RLS Policies for voice_posts
CREATE POLICY "Anyone can view free voice posts" ON voice_posts
    FOR SELECT USING (is_free = true);

CREATE POLICY "Users can view voice posts they have access to" ON voice_posts
    FOR SELECT USING (
        is_free = true OR
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM subscriptions
            WHERE subscriber_id = auth.uid() 
            AND writer_id = voice_posts.user_id
            AND active_until > NOW()
        )
    );

CREATE POLICY "Users can manage their own voice posts" ON voice_posts
    FOR ALL USING (auth.uid() = user_id);

-- 8. Functions for timeline management

-- Function to create timeline post when diary entry is created
CREATE OR REPLACE FUNCTION create_timeline_post_for_diary()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO timeline_posts (
        user_id,
        content_type,
        title,
        description,
        is_free,
        is_hidden,
        diary_entry_id
    ) VALUES (
        NEW.user_id,
        'diary',
        NEW.title,
        LEFT(NEW.body_md, 200) || CASE WHEN LENGTH(NEW.body_md) > 200 THEN '...' ELSE '' END,
        NEW.is_free,
        NEW.is_hidden,
        NEW.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create timeline post when book is published
CREATE OR REPLACE FUNCTION create_timeline_post_for_book()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create timeline post for ebooks when they're published
    IF NEW.is_ebook = true AND NEW.is_complete = true AND (OLD.is_complete = false OR OLD IS NULL) THEN
        INSERT INTO timeline_posts (
            user_id,
            content_type,
            title,
            description,
            is_free,
            is_hidden,
            project_id,
            featured
        ) VALUES (
            NEW.user_id,
            'book',
            NEW.title,
            COALESCE(NEW.meta_description, NEW.description, 'A new book has been published'),
            CASE WHEN NEW.price_amount = 0 THEN true ELSE false END,
            NEW.is_private,
            NEW.id,
            true -- Books are featured by default
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create timeline post when voice post is created
CREATE OR REPLACE FUNCTION create_timeline_post_for_voice()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO timeline_posts (
        user_id,
        content_type,
        title,
        description,
        is_free,
        is_hidden,
        voice_post_id
    ) VALUES (
        NEW.user_id,
        'voice',
        NEW.title,
        NEW.description,
        NEW.is_free,
        false, -- Voice posts are public by default
        NEW.id
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Create triggers
CREATE TRIGGER trigger_diary_timeline_post
    AFTER INSERT ON diary_entries
    FOR EACH ROW
    EXECUTE FUNCTION create_timeline_post_for_diary();

CREATE TRIGGER trigger_book_timeline_post
    AFTER INSERT OR UPDATE ON projects
    FOR EACH ROW
    EXECUTE FUNCTION create_timeline_post_for_book();

CREATE TRIGGER trigger_voice_timeline_post
    AFTER INSERT ON voice_posts
    FOR EACH ROW
    EXECUTE FUNCTION create_timeline_post_for_voice();

-- 10. Update existing diary entries to have timeline posts
INSERT INTO timeline_posts (
    user_id,
    content_type,
    title,
    description,
    is_free,
    is_hidden,
    diary_entry_id,
    created_at,
    updated_at
)
SELECT 
    user_id,
    'diary',
    title,
    LEFT(body_md, 200) || CASE WHEN LENGTH(body_md) > 200 THEN '...' ELSE '' END,
    is_free,
    is_hidden,
    id,
    created_at,
    updated_at
FROM diary_entries
WHERE NOT EXISTS (
    SELECT 1 FROM timeline_posts 
    WHERE diary_entry_id = diary_entries.id
);

-- 11. Update existing published books to have timeline posts
INSERT INTO timeline_posts (
    user_id,
    content_type,
    title,
    description,
    is_free,
    is_hidden,
    project_id,
    featured,
    created_at,
    updated_at
)
SELECT 
    user_id,
    'book',
    title,
    COALESCE(meta_description, description, 'A new book has been published'),
    CASE WHEN price_amount = 0 THEN true ELSE false END,
    is_private,
    id,
    true,
    created_at,
    updated_at
FROM projects
WHERE is_ebook = true 
AND is_complete = true
AND NOT EXISTS (
    SELECT 1 FROM timeline_posts 
    WHERE project_id = projects.id
);

-- 12. Grant permissions
GRANT ALL ON timeline_posts TO authenticated;
GRANT ALL ON voice_posts TO authenticated;
GRANT USAGE ON TYPE content_type TO authenticated;
