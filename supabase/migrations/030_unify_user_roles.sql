-- Migration to unify user roles for universal flow
-- This allows all users to create and consume content

-- 1. Add new unified role to enum
ALTER TYPE user_role ADD VALUE 'user';

-- 2. Update existing users to unified role (except admins)
-- Keep admins as admin, convert everyone else to 'user'
UPDATE users 
SET role = 'user' 
WHERE role IN ('subscriber', 'writer', 'visitor');

-- 3. Update default role for new users
ALTER TABLE users ALTER COLUMN role SET DEFAULT 'user';

-- 4. Update RLS policies to work with unified roles

-- Drop existing role-specific policies
DROP POLICY IF EXISTS "Writers can manage their own entries" ON diary_entries;
DROP POLICY IF EXISTS "Writers can manage photos for their entries" ON photos;
DROP POLICY IF EXISTS "Writers can view their subscribers" ON subscriptions;
DROP POLICY IF EXISTS "Writers can view their followers" ON follows;
DROP POLICY IF EXISTS "Subscribers can comment on entries they're subscribed to" ON comments;
DROP POLICY IF EXISTS "Writers can moderate comments on their entries" ON comments;

-- Create new unified policies for diary entries
CREATE POLICY "Users can manage their own entries" ON diary_entries
    FOR ALL USING (auth.uid() = user_id);

-- Create new unified policies for photos
CREATE POLICY "Users can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = auth.uid()
        )
    );

-- Create new unified policies for subscriptions
CREATE POLICY "Users can view their own subscriptions" ON subscriptions
    FOR SELECT USING (
        auth.uid() = subscriber_id OR auth.uid() = writer_id
    );

CREATE POLICY "Users can create subscriptions as subscriber" ON subscriptions
    FOR INSERT WITH CHECK (auth.uid() = subscriber_id);

CREATE POLICY "Content creators can view their subscribers" ON subscriptions
    FOR SELECT USING (auth.uid() = writer_id);

-- Create new unified policies for follows (if exists)
DROP POLICY IF EXISTS "Users can manage their own follows" ON follows;
DROP POLICY IF EXISTS "Writers can view their followers" ON follows;

CREATE POLICY "Users can manage their own follows" ON follows
    FOR ALL USING (auth.uid() = follower_id);

CREATE POLICY "Content creators can view their followers" ON follows
    FOR SELECT USING (auth.uid() = writer_id);

-- Create new unified policies for comments
CREATE POLICY "Users can comment on entries they have access to" ON comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        (
            -- Can comment on free entries
            EXISTS (
                SELECT 1 FROM diary_entries de 
                WHERE de.id = diary_entry_id AND de.is_free = true
            )
            OR
            -- Can comment on own entries
            EXISTS (
                SELECT 1 FROM diary_entries de 
                WHERE de.id = diary_entry_id AND de.user_id = auth.uid()
            )
            OR
            -- Can comment on subscribed entries
            EXISTS (
                SELECT 1 FROM subscriptions s
                JOIN diary_entries de ON de.user_id = s.writer_id
                WHERE s.subscriber_id = auth.uid() 
                AND de.id = diary_entry_id
                AND s.active_until > NOW()
            )
        )
    );

CREATE POLICY "Content creators can moderate comments on their entries" ON comments
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = auth.uid()
        )
    );

-- Update any functions that check for specific roles
-- Update the subscription function to be role-agnostic
CREATE OR REPLACE FUNCTION user_has_active_subscription(subscriber_uuid UUID, writer_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM subscriptions
        WHERE subscriber_id = subscriber_uuid 
        AND writer_id = writer_uuid
        AND active_until > NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the following function to be role-agnostic
CREATE OR REPLACE FUNCTION is_following(p_writer_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM follows 
        WHERE follower_id = auth.uid() 
        AND writer_id = p_writer_id
    );
END;
$$;

-- Add indexes for performance with new unified structure
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_price_monthly ON users(price_monthly) WHERE price_monthly IS NOT NULL;

-- Add comment explaining the change
COMMENT ON TYPE user_role IS 'Updated to support unified user flow: visitor (not logged in), user (can create and consume), admin (platform management)';
COMMENT ON COLUMN users.role IS 'Unified role system: user can both create and consume content, admin for platform management';
