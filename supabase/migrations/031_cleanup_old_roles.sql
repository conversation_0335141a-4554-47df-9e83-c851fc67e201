-- OPTIONAL: Clean up old role values after migration is tested and stable
-- Run this ONLY after you've verified the unified flow works correctly

-- This migration removes the old role values from the enum
-- WARNING: This is irreversible! Make sure to test thoroughly first

-- Step 1: Verify all users are using the new role system
DO $$
DECLARE
    old_role_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO old_role_count 
    FROM users 
    WHERE role IN ('subscriber', 'writer', 'visitor');
    
    IF old_role_count > 0 THEN
        RAISE EXCEPTION 'Found % users still using old roles. Migration not complete.', old_role_count;
    END IF;
    
    RAISE NOTICE 'All users successfully migrated to new role system';
END $$;

-- Step 2: Create new enum without old values
CREATE TYPE user_role_new AS ENUM ('user', 'admin');

-- Step 3: Update the users table to use new enum
ALTER TABLE users ALTER COLUMN role TYPE user_role_new USING role::text::user_role_new;

-- Step 4: Drop old enum and rename new one
DROP TYPE user_role;
ALTER TYPE user_role_new RENAME TO user_role;

-- Step 5: Update default
ALTER TABLE users ALTER COLUMN role SET DEFAULT 'user';

-- Update comments
COMMENT ON TYPE user_role IS 'Simplified role system: user (can create and consume content), admin (platform management)';
COMMENT ON COLUMN users.role IS 'Simplified role: user for all content creators/consumers, admin for platform management';

-- Verify the change
SELECT 
    role, 
    COUNT(*) as user_count 
FROM users 
GROUP BY role 
ORDER BY role;
