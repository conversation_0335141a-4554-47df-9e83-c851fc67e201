-- Remove PDF support and add document format support
-- This migration updates the ebook_file_type constraints to remove PDF and add document formats

-- First, update any existing PDF records to EPUB (since PDFs were converted to EPUB in the old flow)
UPDATE projects
SET ebook_file_type = 'epub'
WHERE ebook_file_type = 'pdf';

-- Update the ebook_file_type constraint to remove PDF and add document formats
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_ebook_file_type_check;
ALTER TABLE projects ADD CONSTRAINT projects_ebook_file_type_check
    CHECK (ebook_file_type IN ('epub', 'docx', 'doc', 'rtf'));

-- Update storage bucket allowed MIME types for book uploads
UPDATE storage.buckets 
SET allowed_mime_types = ARRAY[
    'application/epub+zip',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', -- .docx
    'application/msword', -- .doc
    'application/rtf', -- .rtf
    'text/rtf' -- alternative RTF MIME type
]
WHERE id = 'book-uploads';

-- Update ebooks bucket allowed MIME types as well
UPDATE storage.buckets 
SET allowed_mime_types = ARRAY[
    'application/epub+zip',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', -- .docx
    'application/msword', -- .doc
    'application/rtf', -- .rtf
    'text/rtf' -- alternative RTF MIME type
]
WHERE id = 'ebooks';

-- Add comment explaining the change
COMMENT ON CONSTRAINT projects_ebook_file_type_check ON projects IS 
'Allowed ebook file types: EPUB (native), DOCX/DOC/RTF (converted to EPUB). PDF support removed.';
