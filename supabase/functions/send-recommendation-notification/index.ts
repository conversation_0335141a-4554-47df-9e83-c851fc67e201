import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { recommenderId, recommendedUserId, recommenderName } = await req.json()

    if (!recommenderId || !recommendedUserId || !recommenderName) {
      throw new Error('Missing required parameters')
    }

    // Create notification record
    const { error: notificationError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: recommendedUserId,
        type: 'recommendation',
        title: 'You\'ve been recommended!',
        message: `${recommenderName} added you to their recommended creators list`,
        data: {
          recommender_id: recommenderId,
          recommender_name: recommenderName
        }
      })

    if (notificationError) {
      throw notificationError
    }

    // Optional: Send email notification (if you have email setup)
    // You could integrate with Resend here

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Error sending recommendation notification:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})
