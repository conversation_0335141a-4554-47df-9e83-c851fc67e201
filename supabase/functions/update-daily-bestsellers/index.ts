import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('Starting daily bestsellers update...')

    // Call the database function to update bestsellers
    const { data, error } = await supabaseClient.rpc('update_daily_bestsellers')

    if (error) {
      console.error('Error updating daily bestsellers:', error)
      throw error
    }

    console.log('Daily bestsellers updated successfully')

    // Get counts for verification
    const { data: freeBestsellers } = await supabaseClient
      .from('daily_bestsellers')
      .select('*', { count: 'exact', head: true })
      .eq('date', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .eq('book_type', 'free')

    const { data: paidBestsellers } = await supabaseClient
      .from('daily_bestsellers')
      .select('*', { count: 'exact', head: true })
      .eq('date', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .eq('book_type', 'paid')

    const result = {
      success: true,
      message: 'Daily bestsellers updated successfully',
      date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      counts: {
        free_bestsellers: freeBestsellers?.length || 0,
        paid_bestsellers: paidBestsellers?.length || 0
      },
      timestamp: new Date().toISOString()
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Error in update-daily-bestsellers function:', error)
    return new Response(JSON.stringify({ 
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
