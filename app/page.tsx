import Link from "next/link"
import { UserRedirect } from "@/components/ReaderRedirect";
import { StructuredData } from "@/components/StructuredData";

export default function Home() {
  return (
    <>
      <UserRedirect />
      <StructuredData type="website" data={{}} />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="text-center mb-12 sm:mb-16">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-serif mb-4 sm:mb-6 text-gray-800 leading-tight">
            OnlyDiary
          </h1>
          <div className="max-w-4xl mx-auto mb-8">
            <p className="text-lg sm:text-xl font-serif text-gray-600 leading-relaxed mb-6">
              A new kind of social network — built on diary entries instead of posts.
            </p>
            <p className="text-lg sm:text-xl font-serif text-gray-600 leading-relaxed mb-6">
              Write your truth. Share your story. Let people know the real you.
            </p>
            <p className="text-lg sm:text-xl font-serif text-gray-600 leading-relaxed mb-6">
              Some write to heal. Some write to be heard. Some read to grow.
            </p>
            <p className="text-xl sm:text-2xl font-serif text-gray-800 font-medium leading-relaxed">
              Real stories from real people.
            </p>
          </div>

          {/* Unified CTA */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
            <Link
              href="/register"
              className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] shadow-lg"
            >
              Join OnlyDiary
            </Link>
            <Link
              href="/timeline"
              className="w-full sm:w-auto text-gray-600 hover:text-gray-800 font-medium transition-colors"
            >
              Browse Stories →
            </Link>
          </div>
        </div>

        {/* How OnlyDiary Works */}
        <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 sm:p-12 shadow-lg border border-gray-100/50 mb-12 sm:mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-serif mb-4 text-gray-800">How OnlyDiary Works</h2>
            <p className="text-gray-600 font-serif text-lg max-w-2xl mx-auto leading-relaxed">
              The natural progression from sharing your truth to receiving genuine support
            </p>
          </div>

          <div className="space-y-12 sm:space-y-16">
            {/* Step 1 */}
            <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
              <div className="lg:w-1/2 text-center lg:text-left">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 text-blue-600 rounded-full font-bold text-lg mb-4">1</div>
                <h3 className="text-2xl sm:text-3xl font-serif mb-4 text-gray-800">📖 Share Your Truth</h3>
                <p className="text-gray-600 font-serif text-lg leading-relaxed mb-4">
                  Write diary entries, record voice notes, publish complete books.
                </p>
                <p className="text-gray-600 font-serif leading-relaxed">
                  Content too personal for social media, shared safely in our intimate community.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
                  <div className="text-center">
                    <div className="text-4xl mb-4">✍️</div>
                    <p className="text-gray-700 font-medium">Smart formatting & distraction-free writing</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
              <div className="lg:w-1/2 text-center lg:text-left">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 text-purple-600 rounded-full font-bold text-lg mb-4">2</div>
                <h3 className="text-2xl sm:text-3xl font-serif mb-4 text-gray-800">❤️ Build Real Connections</h3>
                <p className="text-gray-600 font-serif text-lg leading-relaxed mb-4">
                  Readers get to know the authentic you through your stories.
                </p>
                <p className="text-gray-600 font-serif leading-relaxed">
                  Form genuine relationships, not just follower counts. Read and publish books directly in our beautiful e-reader.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-100">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📚</div>
                    <p className="text-gray-700 font-medium">Built-in e-reader for seamless book publishing & reading</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
              <div className="lg:w-1/2 text-center lg:text-left">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 text-green-600 rounded-full font-bold text-lg mb-4">3</div>
                <h3 className="text-2xl sm:text-3xl font-serif mb-4 text-gray-800">💝 Receive Natural Support</h3>
                <p className="text-gray-600 font-serif text-lg leading-relaxed mb-4">
                  When people truly know you, they naturally want to support you.
                </p>
                <p className="text-gray-600 font-serif leading-relaxed">
                  Keep 80% of subscriptions + 95% of donations. Instant withdrawals with real-time engagement.
                </p>
              </div>
              <div className="lg:w-1/2">
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-100">
                  <div className="text-center">
                    <div className="text-4xl mb-4">💰</div>
                    <p className="text-gray-700 font-medium">Authentic income from authentic connections</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Final CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl p-8 sm:p-12 shadow-lg max-w-2xl mx-auto">
            <h3 className="text-2xl sm:text-3xl font-serif mb-4 text-white">Ready to share your truth?</h3>
            <p className="text-gray-300 font-serif mb-8 text-lg leading-relaxed">
              Join a community where authentic stories create genuine connections and meaningful support.
            </p>
            <Link
              href="/register"
              className="inline-block bg-white text-gray-900 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] shadow-lg"
            >
              Start Your Journey
            </Link>
          </div>
        </div>
      </div>
    </div>
    </>
  );
}
