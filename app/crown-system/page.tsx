import { createServerSupabaseClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Crown, Star, Zap, Shield, Award, TrendingUp, Users, Sparkles } from 'lucide-react'

export default async function CrownSystemPage() {
  const supabase = await createServerSupabaseClient()

  // Check if user is authenticated
  const { data: { user } } = await supabase.auth.getUser()

  let userData = null
  if (user) {
    // Get user data to check if they're in first 10K
    const { data } = await supabase
      .from('users')
      .select('signup_number, has_day1_badge')
      .eq('id', user.id)
      .single()
    userData = data
  }

  // Show access denied message for unauthenticated users or users not in first 10K
  if (!user || !userData?.signup_number || userData.signup_number > 10000) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 py-8 sm:py-12">

          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Crown className="text-purple-600" size={40} />
              <h1 className="text-3xl sm:text-4xl font-serif text-gray-800">The Crown System</h1>
              <Crown className="text-purple-600" size={40} />
            </div>
          </div>

          {/* Access Denied Message */}
          <div className="bg-white border border-gray-200 rounded-lg p-8 shadow-sm">
            <div className="text-center">
              <Shield className="text-gray-400 mx-auto mb-4" size={48} />
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                {!user ? 'Authentication Required' : 'Exclusive Access Only'}
              </h2>

              {!user ? (
                <div className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">
                    The Crown System is an exclusive feature available only to OnlyDiary members.
                    You need to be logged in to access this information.
                  </p>
                  <div className="pt-4">
                    <a
                      href="/auth"
                      className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      Sign In to Continue
                    </a>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">
                    The Crown System is an exclusive feature available only to the first 10,000 OnlyDiary members.
                    This information is not accessible to newer signups as part of our founding member benefits.
                  </p>
                  <p className="text-sm text-gray-500">
                    Your signup number: #{userData?.signup_number?.toLocaleString() || 'Unknown'}
                  </p>
                  <div className="pt-4">
                    <a
                      href="/timeline"
                      className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Return to Timeline
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Crown className="text-purple-600" size={40} />
            <h1 className="text-4xl sm:text-5xl font-serif text-gray-800">The Crown System</h1>
            <Crown className="text-purple-600" size={40} />
          </div>
          <p className="text-xl text-gray-600 font-serif max-w-2xl mx-auto">
            A revolutionary approach to community ownership and value creation
          </p>
        </div>

        {/* Exclusive Access Notice */}
        <div className="bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200 rounded-lg p-6 mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Sparkles className="text-purple-600" size={24} />
            <h2 className="text-lg font-semibold text-purple-800">Exclusive Access</h2>
          </div>
          <p className="text-purple-700">
            You're seeing this page because you're among the first 10,000 OnlyDiary members. 
            This information will not be available to future signups.
          </p>
        </div>

        {/* Crown Tiers */}
        <div className="mb-12">
          <h2 className="text-3xl font-serif text-gray-800 text-center mb-8">The Five Crown Tiers</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            <div className="bg-white border border-purple-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-purple-50 border border-purple-200 rounded-full flex items-center justify-center">
                  <Crown size={18} className="text-purple-600" />
                </div>
                <h3 className="font-semibold text-purple-800">Day 1</h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">Members 1-500</p>
              <p className="text-gray-700">The founding community. The rarest and most prestigious crown.</p>
            </div>

            <div className="bg-white border border-yellow-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-yellow-50 border border-yellow-200 rounded-full flex items-center justify-center">
                  <Crown size={18} className="text-yellow-600" />
                </div>
                <h3 className="font-semibold text-yellow-800">Pioneer</h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">Members 501-1,000</p>
              <p className="text-gray-700">Early believers who joined the movement in its infancy.</p>
            </div>

            <div className="bg-white border border-gray-300 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-gray-50 border border-gray-300 rounded-full flex items-center justify-center">
                  <Crown size={18} className="text-gray-600" />
                </div>
                <h3 className="font-semibold text-gray-800">Founder</h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">Members 1,001-2,500</p>
              <p className="text-gray-700">Foundational members who helped establish the community.</p>
            </div>

            <div className="bg-white border border-amber-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-amber-50 border border-amber-200 rounded-full flex items-center justify-center">
                  <Crown size={18} className="text-amber-700" />
                </div>
                <h3 className="font-semibold text-amber-800">Early Adopter</h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">Members 2,501-5,000</p>
              <p className="text-gray-700">Forward-thinking creators who recognized the potential early.</p>
            </div>

            <div className="bg-white border border-blue-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-blue-50 border border-blue-200 rounded-full flex items-center justify-center">
                  <Crown size={18} className="text-blue-600" />
                </div>
                <h3 className="font-semibold text-blue-800">Charter Member</h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">Members 5,001-10,000</p>
              <p className="text-gray-700">Charter members who joined before the system was complete.</p>
            </div>

          </div>
        </div>

        {/* Value Innovation */}
        <div className="bg-white border border-gray-200 rounded-lg p-8 mb-8 shadow-sm">
          <div className="flex items-center gap-3 mb-6">
            <TrendingUp className="text-green-600" size={32} />
            <h2 className="text-3xl font-serif text-gray-800">Value Innovation</h2>
          </div>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Community Growth = Crown Value</h3>
              <p className="text-gray-700 leading-relaxed">
                As OnlyDiary grows from thousands to millions of users, the scarcity and prestige of early crowns 
                naturally increases. What starts as a community badge evolves into a symbol of pioneering status 
                in the creator economy.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">The Advertising Revolution</h3>
              <p className="text-gray-700 leading-relaxed">
                When our innovative advertising system launches, crowns will unlock exclusive opportunities. 
                Premium advertising placements, creator spotlights, and platform features will be accessible 
                through crown ownership, creating genuine utility and demand.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Digital Ownership Rights</h3>
              <p className="text-gray-700 leading-relaxed">
                Your crown represents more than status—it's a stake in OnlyDiary's future. As the platform 
                evolves, crown holders will have unique access to new features, exclusive content, and 
                community governance opportunities.
              </p>
            </div>
          </div>
        </div>

        {/* Future Vision */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-8 mb-8">
          <div className="flex items-center gap-3 mb-6">
            <Users className="text-blue-600" size={32} />
            <h2 className="text-3xl font-serif text-gray-800">The Future Vision</h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Marketplace Potential</h3>
              <p className="text-gray-700">
                Imagine a future where your Day 1 crown becomes a coveted digital asset, 
                tradeable among collectors and creators who value authentic community membership.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Platform Governance</h3>
              <p className="text-gray-700">
                Crown holders may gain voting rights on platform decisions, feature development, 
                and community guidelines—true ownership in the platform you helped build.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Creator Benefits</h3>
              <p className="text-gray-700">
                Enhanced profile visibility, priority customer support, early access to new features, 
                and exclusive creator tools reserved for crown holders.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Network Effects</h3>
              <p className="text-gray-700">
                As OnlyDiary becomes the premier creator platform, your crown becomes a symbol 
                of being an original architect of the creator economy revolution.
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg p-8">
          <Crown className="mx-auto mb-4 text-yellow-300" size={48} />
          <h2 className="text-2xl font-serif mb-4">You're Already Part of History</h2>
          <p className="text-lg mb-6 opacity-90">
            By being among the first 10,000 members, you've secured your place in OnlyDiary's founding story. 
            Your crown isn't just a badge—it's a key to the future of authentic creator communities.
          </p>
          <p className="text-sm opacity-75">
            The Crown System represents our commitment to rewarding those who believe in authentic storytelling 
            and genuine creator-audience connections.
          </p>
        </div>

      </div>
    </div>
  )
}
