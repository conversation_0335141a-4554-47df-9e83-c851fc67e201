import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { parentPostId, audioUrl, audioKey, duration, description } = await request.json()

    // Validate required fields
    if (!parentPostId || !audioUrl || !audioKey || !duration) {
      return NextResponse.json({
        error: 'Missing required fields: parentPostId, audioUrl, audioKey, duration'
      }, { status: 400 })
    }

    // Create the audio reply record
    const { data: reply, error: replyError } = await supabase
      .from('audio_replies')
      .insert({
        user_id: user.id,
        audio_post_id: parentPostId, // Use audio_post_id to match your schema
        audio_url: audioUrl,
        audio_key: audioKey,
        duration_seconds: duration,
        file_size_bytes: 0 // Default value since it's in your schema
      })
      .select('*')
      .single()

    if (replyError) {
      console.error('Error creating audio reply:', replyError)
      return NextResponse.json({ error: 'Failed to create audio reply' }, { status: 500 })
    }

    // Update the parent post's reply count
    // First get the current count
    const { data: currentPost } = await supabase
      .from('audio_posts')
      .select('reply_count')
      .eq('id', parentPostId)
      .single()

    if (currentPost) {
      const { error: updateError } = await supabase
        .from('audio_posts')
        .update({
          reply_count: (currentPost.reply_count || 0) + 1
        })
        .eq('id', parentPostId)

      if (updateError) {
        console.error('Error updating reply count:', updateError)
        // Don't fail the request, just log the error
      }
    }

    return NextResponse.json({ 
      success: true, 
      reply,
      message: 'Audio reply posted successfully!' 
    })
  } catch (error) {
    console.error('Audio reply creation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
