import { NextRequest, NextResponse } from 'next/server'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { r2Client, AUDIO_BUCKET, generateAudioKey, getAudioUrl } from '@/lib/cloudflare-r2'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Audio reply API called')
    
    // Verify user authentication
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    console.log('Auth check:', { user: !!user, authError })
    
    if (authError || !user) {
      console.log('Authentication failed:', authError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    console.log('Request body:', body)
    
    const { parentPostId, duration, description } = body
    
    // Validate input
    if (!parentPostId) {
      console.log('Missing parentPostId')
      return NextResponse.json({ error: 'Parent post ID is required' }, { status: 400 })
    }
    
    if (!duration || duration > 9.2) { // Allow slight floating point precision buffer
      console.log('Invalid duration:', duration)
      return NextResponse.json({ error: 'Duration must be 9 seconds or less' }, { status: 400 })
    }

    console.log('Generating audio reply key for user:', user.id)
    
    // Generate unique key for the audio reply file
    const key = generateAudioKey(user.id, 'reply')
    console.log('Generated key:', key)
    
    // Create presigned URL for upload
    const command = new PutObjectCommand({
      Bucket: AUDIO_BUCKET,
      Key: key,
      ContentType: 'audio/webm',
      Metadata: {
        userId: user.id,
        type: 'reply',
        parentPostId: parentPostId,
        duration: duration.toString(),
        uploadedAt: new Date().toISOString(),
      },
    })

    console.log('Creating presigned URL for reply...')
    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 }) // 1 hour
    const publicUrl = getAudioUrl(key)
    
    console.log('Reply upload URL created successfully')
    
    return NextResponse.json({
      uploadUrl,
      key,
      publicUrl,
      parentPostId,
      duration: Math.round(duration * 10) / 10 // Round to 1 decimal place
    })
  } catch (error: any) {
    console.error('Audio reply upload error:', error)
    return NextResponse.json({ error: 'Upload failed', details: error.message }, { status: 500 })
  }
}
