import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const postId = params.id
    
    // Get user if authenticated (optional for plays)
    const { data: { user } } = await supabase.auth.getUser()
    
    // Get client IP and user agent for anonymous tracking
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || '127.0.0.1'
    const userAgent = request.headers.get('user-agent') || ''

    // Try to record the play (will be ignored if duplicate within same day)
    const { error } = await supabase
      .from('audio_plays')
      .insert({
        audio_post_id: postId,
        user_id: user?.id || null,
        ip_address: ip,
        user_agent: userAgent
      })

    // Don't return error for duplicate plays - just ignore silently
    if (error && !error.message.includes('duplicate key')) {
      console.error('Error recording audio play:', error)
      return NextResponse.json({ error: 'Failed to record play' }, { status: 500 })
    }

    // Get updated play count and achievements
    const { data: postData } = await supabase
      .from('audio_posts')
      .select(`
        play_count,
        achievements:audio_achievements(
          achievement_type,
          play_count_achieved,
          achieved_at
        )
      `)
      .eq('id', postId)
      .single()

    return NextResponse.json({ 
      success: true,
      play_count: postData?.play_count || 0,
      achievements: postData?.achievements || []
    })
  } catch (error) {
    console.error('Audio play API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
