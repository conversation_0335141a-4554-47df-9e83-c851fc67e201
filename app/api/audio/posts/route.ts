import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

// GET - Fetch audio posts for timeline
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const { data: posts, error } = await supabase
      .from('audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching audio posts:', error)
      return NextResponse.json({ error: 'Failed to fetch posts' }, { status: 500 })
    }

    return NextResponse.json({ posts })
  } catch (error) {
    console.error('Audio posts API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create new audio post
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { 
      audio_url, 
      audio_key, 
      description, 
      duration_seconds, 
      file_size_bytes 
    } = await request.json()

    // Validate input
    if (!audio_url || !audio_key || !duration_seconds || !file_size_bytes) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    if (duration_seconds > 9.0) {
      return NextResponse.json({ error: 'Duration cannot exceed 9 seconds' }, { status: 400 })
    }

    if (description && description.length > 50) {
      return NextResponse.json({ error: 'Description cannot exceed 50 characters' }, { status: 400 })
    }

    // Create audio post
    const { data: post, error } = await supabase
      .from('audio_posts')
      .insert({
        user_id: user.id,
        audio_url,
        audio_key,
        description: description || null,
        duration_seconds,
        file_size_bytes
      })
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url
        )
      `)
      .single()

    if (error) {
      console.error('Error creating audio post:', error)
      return NextResponse.json({ error: 'Failed to create post' }, { status: 500 })
    }

    return NextResponse.json({ post })
  } catch (error) {
    console.error('Create audio post error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
