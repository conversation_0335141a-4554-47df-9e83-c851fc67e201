import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { recommenderId, recommendedUserId, recommenderName } = await request.json()

    if (!recommenderId || !recommendedUserId || !recommenderName) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    const supabase = createSupabaseClient()

    // Create notification record
    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: recommendedUserId,
        type: 'recommendation',
        title: 'You\'ve been recommended!',
        message: `${recommenderName} added you to their recommended creators list`,
        data: {
          recommender_id: recommenderId,
          recommender_name: recommenderName
        }
      })

    if (notificationError) {
      console.error('Error creating notification:', notificationError)
      return NextResponse.json(
        { error: 'Failed to create notification' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error sending recommendation notification:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
