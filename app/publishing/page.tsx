"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { BookManager } from "@/components/BookManager"

interface Book {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  book_type: string
  price_amount: number
  average_rating: number
  review_count: number
  sales_count: number
  is_ebook: boolean
  is_complete: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

interface User {
  id: string
  name: string
  role: string
}

export default function PublishingCenter() {
  const [user, setUser] = useState<User | null>(null)
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [totalEarnings, setTotalEarnings] = useState(0)
  const [monthlyEarnings, setMonthlyEarnings] = useState(0)
  const router = useRouter()
  const supabase = createSupabaseClient()

  const handleNavigation = async (path: string) => {
    setNavigating(path)
    router.push(path)
  }

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  const checkAuthAndFetchData = async () => {
    try {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile) {
        router.push('/login')
        return
      }

      // All authenticated users can publish (unified experience)
      if (profile.role !== 'user' && profile.role !== 'admin') {
        router.push('/timeline')
        return
      }

      setUser(profile)
      await fetchBooks(authUser.id)
      await fetchEarnings(authUser.id)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchBooks = async (userId: string) => {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })

    if (!error && data) {
      setBooks(data)
    }
  }

  const fetchEarnings = async (userId: string) => {
    // Calculate earnings from book sales (80% royalty)
    const { data: purchases } = await supabase
      .from('book_purchases')
      .select(`
        amount_paid,
        created_at,
        projects!inner(user_id)
      `)
      .eq('projects.user_id', userId)
      .eq('status', 'completed')

    if (purchases) {
      const total = purchases.reduce((sum, purchase) => sum + (purchase.amount_paid * 0.8), 0)
      setTotalEarnings(total)

      // Calculate this month's earnings
      const thisMonth = new Date()
      thisMonth.setDate(1)
      const monthlyPurchases = purchases.filter(p => new Date(p.created_at) >= thisMonth)
      const monthly = monthlyPurchases.reduce((sum, purchase) => sum + (purchase.amount_paid * 0.8), 0)
      setMonthlyEarnings(monthly)
    }
  }

  const formatPrice = (cents: number) => {
    if (cents === 0) return "Free"
    return `$${(cents / 100).toFixed(2)}`
  }

  const formatEarnings = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Publishing Center...</p>
        </div>
      </div>
    )
  }

  const publishedBooks = books.filter(book => book.is_ebook && book.is_complete)
  const draftBooks = books.filter(book => !book.is_ebook || !book.is_complete)

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Mobile-First Header */}
      <div className="bg-white border-b border-gray-100 sticky top-0 z-10 backdrop-blur-sm bg-white/95">
        <div className="max-w-6xl mx-auto px-4 py-4 sm:py-6">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl mb-3">
              <span className="text-2xl">📚</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2">
              Publishing Center
            </h1>
            <p className="text-sm sm:text-base text-gray-600 max-w-md mx-auto">
              Manage your books, track sales, and grow your author business
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-6 space-y-6">

        {/* Mobile-Optimized Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <div className="bg-white rounded-2xl p-4 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl mb-2 sm:mb-3">
                <span className="text-lg sm:text-xl">💰</span>
              </div>
              <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Earnings</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">{formatEarnings(totalEarnings)}</p>
              <p className="text-xs text-green-600 mt-1">80% royalty*</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-4 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl mb-2 sm:mb-3">
                <span className="text-lg sm:text-xl">📈</span>
              </div>
              <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">This Month</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">{formatEarnings(monthlyEarnings)}</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-4 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-400 to-violet-500 rounded-xl mb-2 sm:mb-3">
                <span className="text-lg sm:text-xl">📚</span>
              </div>
              <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Published</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">{publishedBooks.length}</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-4 sm:p-5 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-orange-400 to-red-400 rounded-xl mb-2 sm:mb-3">
                <span className="text-lg sm:text-xl">✏️</span>
              </div>
              <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Drafts</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-900">{draftBooks.length}</p>
            </div>
          </div>
        </div>

        {/* Mobile-First Quick Actions */}
        <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-4 sm:mb-5">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <span className="text-sm">⚡</span>
            </div>
            <h2 className="text-lg sm:text-xl font-serif text-gray-900">Quick Actions</h2>
          </div>

          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-4">
            <Button
              onClick={() => handleNavigation('/write/upload-ebook')}
              disabled={navigating === '/write/upload-ebook'}
              className="w-full h-12 sm:h-14 bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 rounded-xl font-medium text-sm sm:text-base shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {navigating === '/write/upload-ebook' ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Publishing...
                </>
              ) : (
                <>
                  <span className="mr-2">📖</span>
                  Publish New Book
                </>
              )}
            </Button>

            <Button
              onClick={() => handleNavigation('/write/diary')}
              disabled={navigating === '/write/diary'}
              variant="outline"
              className="w-full h-12 sm:h-14 border-2 border-gray-200 hover:border-purple-300 hover:bg-purple-50 rounded-xl font-medium text-sm sm:text-base transition-all duration-200"
            >
              {navigating === '/write/diary' ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Loading...
                </>
              ) : (
                <>
                  <span className="mr-2">✏️</span>
                  Start Writing
                </>
              )}
            </Button>

            <Button
              onClick={() => handleNavigation('/books')}
              disabled={navigating === '/books'}
              variant="outline"
              className="w-full h-12 sm:h-14 border-2 border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 rounded-xl font-medium text-sm sm:text-base transition-all duration-200"
            >
              {navigating === '/books' ? (
                <>
                  <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Loading...
                </>
              ) : (
                <>
                  <span className="mr-2">🛍️</span>
                  Book Store
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Published Books - Mobile Optimized */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
          <div className="p-4 sm:p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-violet-500 rounded-xl flex items-center justify-center">
                  <span className="text-lg">📚</span>
                </div>
                <div>
                  <h2 className="text-lg sm:text-xl font-serif text-gray-900">Published Books</h2>
                  <p className="text-xs sm:text-sm text-gray-500">
                    {publishedBooks.length} book{publishedBooks.length !== 1 ? 's' : ''} in your store
                  </p>
                </div>
              </div>
              <Button
                onClick={() => handleNavigation('/write/upload-ebook')}
                disabled={navigating === '/write/upload-ebook'}
                className="hidden sm:flex bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 font-medium rounded-xl px-4 py-2"
              >
                {navigating === '/write/upload-ebook' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Publishing...
                  </>
                ) : (
                  <>
                    <span className="mr-2">+</span>
                    Publish New Book
                  </>
                )}
              </Button>
            </div>
          </div>

          <div className="p-4 sm:p-6">
            {publishedBooks.length === 0 ? (
              <div className="text-center py-12 sm:py-16">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6">
                  <span className="text-2xl sm:text-3xl">📖</span>
                </div>
                <h3 className="text-lg sm:text-xl font-serif text-gray-900 mb-2 sm:mb-3">Ready to share your story?</h3>
                <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8 max-w-sm sm:max-w-md mx-auto leading-relaxed px-4">
                  Upload your finished book and start earning 80% royalties from every sale in the OnlyDiary Book Store.
                </p>
                <Button
                  onClick={() => handleNavigation('/write/upload-ebook')}
                  disabled={navigating === '/write/upload-ebook'}
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {navigating === '/write/upload-ebook' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Publishing...
                    </>
                  ) : (
                    <>
                      <span className="mr-2">📚</span>
                      Publish Your First Book
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {publishedBooks.map((book) => (
                  <BookManager
                    key={book.id}
                    book={book}
                    onUpdate={() => fetchBooks(user!.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Draft Books - Mobile Optimized */}
        {draftBooks.length > 0 && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
            <div className="p-4 sm:p-6 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-400 rounded-lg flex items-center justify-center">
                  <span className="text-sm">✏️</span>
                </div>
                <div>
                  <h2 className="text-lg sm:text-xl font-serif text-gray-900">Draft Books & Projects</h2>
                  <p className="text-xs sm:text-sm text-gray-500 mt-1">Complete these to publish in your store</p>
                </div>
              </div>
            </div>

            <div className="p-4 sm:p-6">
              <div className="space-y-3 sm:space-y-4">
                {draftBooks.map((book) => (
                  <BookManager
                    key={book.id}
                    book={book}
                    onUpdate={() => fetchBooks(user!.id)}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Mobile-Friendly Footnote */}
        <div className="bg-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-100">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg mb-2">
              <span className="text-xs">ℹ️</span>
            </div>
            <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
              <strong>Author Royalties:</strong> You earn 80% of all book sales*<br />
              <span className="text-gray-500">* After Stripe processing fees (2.9% + 30¢ per transaction)</span>
            </p>
          </div>
        </div>

      </div>
    </div>
  )
}
