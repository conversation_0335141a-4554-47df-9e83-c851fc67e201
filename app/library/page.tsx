'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { StarLoader } from '@/components/ui/star-loader'

interface LibraryBook {
  id: string
  title: string
  cover_image_url: string | null
  author_name: string
  access_type: 'purchased' | 'free' | 'preview'
  added_at: string
  last_accessed_at: string
  total_chapters: number
  total_words: number
  reading_time_minutes: number
  users: {
    name: string
    avatar: string | null
  }
}

export default function LibraryPage() {
  const [books, setBooks] = useState<LibraryBook[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [readingBookId, setReadingBookId] = useState<string | null>(null)
  const [deletingBookId, setDeletingBookId] = useState<string | null>(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [bookToDelete, setBookToDelete] = useState<LibraryBook | null>(null)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/auth/login')
      return
    }
    setUser(user)
    fetchLibraryBooks(user.id)
  }

  const fetchLibraryBooks = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_library')
        .select(`
          access_type,
          added_at,
          last_accessed_at,
          projects!inner(
            id,
            title,
            cover_image_url,
            author_name,
            total_chapters,
            total_words,
            reading_time_minutes,
            users!inner(name, avatar)
          )
        `)
        .eq('user_id', userId)
        .order('added_at', { ascending: false })

      if (error) {
        console.error('Error fetching library:', error)
        return
      }

      const libraryBooks = data?.map((item: any) => ({
        id: item.projects.id,
        title: item.projects.title,
        cover_image_url: item.projects.cover_image_url,
        author_name: item.projects.author_name,
        access_type: item.access_type,
        added_at: item.added_at,
        last_accessed_at: item.last_accessed_at,
        total_chapters: item.projects.total_chapters,
        total_words: item.projects.total_words,
        reading_time_minutes: item.projects.reading_time_minutes,
        users: item.projects.users
      })) || []

      setBooks(libraryBooks)
    } catch (error) {
      console.error('Error fetching library books:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatReadingTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const formatPages = (words: number) => {
    return Math.ceil((words / 1000) * 4)
  }

  const handleReadClick = (bookId: string) => {
    setReadingBookId(bookId)
    // All books now use the same EPUB reader (PDFs are converted to EPUB)
    router.push(`/books/${bookId}/read`)
  }

  const handleDeleteClick = (book: LibraryBook) => {
    setBookToDelete(book)
    setShowDeleteModal(true)
  }

  const handleDeleteConfirm = async () => {
    if (!bookToDelete || !user) return

    setDeletingBookId(bookToDelete.id)
    try {
      const { error } = await supabase
        .from('user_library')
        .delete()
        .eq('user_id', user.id)
        .eq('project_id', bookToDelete.id)

      if (error) {
        console.error('Error deleting book from library:', error)
        alert('Failed to remove book from library. Please try again.')
        return
      }

      // Remove book from local state
      setBooks(books.filter(book => book.id !== bookToDelete.id))
      setShowDeleteModal(false)
      setBookToDelete(null)
    } catch (error) {
      console.error('Error deleting book:', error)
      alert('Failed to remove book from library. Please try again.')
    } finally {
      setDeletingBookId(null)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false)
    setBookToDelete(null)
  }

  // Reset loading state when component unmounts or after timeout
  useEffect(() => {
    if (readingBookId) {
      const timeout = setTimeout(() => {
        setReadingBookId(null)
      }, 3000) // Reset after 3 seconds as fallback

      return () => clearTimeout(timeout)
    }
  }, [readingBookId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
          {/* Header */}
          <div className="mb-6 sm:mb-8 flex flex-col items-center justify-center w-full">
            <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2 text-center">My Library</h1>
            <p className="text-sm sm:text-base text-gray-600 text-center">Your collection of purchased and free books</p>
          </div>

          {/* Enhanced Loading Content */}
          <div className="text-center py-12">
            {/* Enhanced Loading Spinner */}
            <div className="relative inline-flex items-center justify-center mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent absolute"></div>
              <div className="absolute text-2xl">📖</div>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Your Library...</h2>
            <p className="text-gray-600">Gathering your books and reading progress</p>

            {/* Loading skeleton for library books */}
            <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-20 bg-gray-200 rounded flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
                      <div className="h-8 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8 flex flex-col items-center justify-center w-full">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900 mb-2 text-center">My Library</h1>
          <p className="text-sm sm:text-base text-gray-600 text-center">Your collection of purchased and free books</p>
        </div>

        {books.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Your library is empty</h2>
            <p className="text-gray-600 mb-6">Start building your collection by purchasing or downloading free books</p>
            <Link href="/books">
              <Button size="lg">Browse Books</Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 sm:gap-4">
            {books.map((book) => (
              <div key={book.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
                {/* Book Cover */}
                <div className="aspect-[3/4] relative bg-gradient-to-br from-gray-50 to-gray-100">
                  {book.cover_image_url ? (
                    <Image
                      src={book.cover_image_url}
                      alt={book.title}
                      fill
                      className="object-contain p-1"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <div className="text-3xl sm:text-4xl">📖</div>
                    </div>
                  )}

                  {/* Access Type Badge */}
                  <div className="absolute top-1 right-1">
                    <span className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${
                      book.access_type === 'purchased'
                        ? 'bg-green-100 text-green-800'
                        : book.access_type === 'free'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {book.access_type === 'purchased' ? 'Owned' :
                       book.access_type === 'free' ? 'Free' : 'Preview'}
                    </span>
                  </div>
                </div>

                {/* Book Info */}
                <div className="p-2 sm:p-3">
                  <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 text-sm sm:text-base">{book.title}</h3>
                  <p className="text-xs sm:text-sm text-gray-600 mb-2">by {book.users.name}</p>

                  {/* Book Stats */}
                  <div className="text-xs text-gray-500 mb-2 space-y-0.5">
                    <div className="flex justify-between">
                      <span>{book.total_chapters} ch</span>
                      <span>~{formatPages(book.total_words)}p</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{formatReadingTime(book.reading_time_minutes)}</span>
                      <span className="hidden sm:inline">Added {new Date(book.added_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-1.5">
                    <Button
                      className="w-full text-xs sm:text-sm h-8 sm:h-9"
                      size="sm"
                      onClick={() => handleReadClick(book.id)}
                      disabled={readingBookId === book.id}
                    >
                      {readingBookId === book.id ? (
                        <StarLoader size="sm" />
                      ) : (
                        <>📖 {book.access_type === 'preview' ? 'Preview' : 'Read'}</>
                      )}
                    </Button>
                    <div className="flex gap-1">
                      <Link href={`/books/${book.id}`} className="flex-1">
                        <Button variant="outline" className="w-full text-xs sm:text-sm h-7 sm:h-8" size="sm">
                          📋 Details
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(book)}
                        disabled={deletingBookId === book.id}
                        className="h-7 sm:h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                      >
                        {deletingBookId === book.id ? (
                          <StarLoader size="sm" />
                        ) : (
                          '🗑️'
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && bookToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="text-center">
              <div className="text-4xl mb-4">🗑️</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Remove from Library?
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to remove "<strong>{bookToDelete.title}</strong>" from your library?
                {bookToDelete.access_type === 'purchased' && (
                  <span className="block mt-2 text-sm text-amber-600">
                    ⚠️ You purchased this book. You can re-download it anytime from the book's page.
                  </span>
                )}
              </p>

              <div className="flex gap-3 justify-center">
                <Button
                  variant="outline"
                  onClick={handleDeleteCancel}
                  disabled={deletingBookId === bookToDelete.id}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={deletingBookId === bookToDelete.id}
                  isLoading={deletingBookId === bookToDelete.id}
                >
                  {deletingBookId === bookToDelete.id ? 'Removing...' : 'Remove from Library'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
