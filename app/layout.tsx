import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import "../styles/typography.css";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";
import { PushNotificationPrompt } from "@/components/PushNotificationPrompt";
import { GoogleAnalytics, PerformanceMonitor } from "@/components/Analytics";
import { ConnectionStatus } from "@/components/ConnectionStatus";
import { Analytics } from "@vercel/analytics/next";
import { NavigationProvider } from "@/contexts/NavigationContext";
import { Suspense } from "react";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export const metadata: Metadata = {
  title: {
    default: "OnlyDiary - Your Words Could be Worth Millions",
    template: "%s | OnlyDiary"
  },
  description: "Turn your authentic stories into income. Write diary entries and books, set your own prices, accept donations, and keep 80% of earnings with instant payouts.",
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/favicon-96x96.png", sizes: "96x96", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
  },
  manifest: "/site.webmanifest",
  keywords: [
    "diary",
    "personal journal",
    "writing platform",
    "storytelling",
    "subscription content",
    "writers",
    "personal stories",
    "daily reflections",
    "creative writing",
    "memoir",
    "life stories",
    "authentic writing"
  ],
  authors: [{ name: "OnlyDiary Team" }],
  creator: "OnlyDiary",
  publisher: "OnlyDiary",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app',
    siteName: 'OnlyDiary',
    title: 'OnlyDiary - Your Words Could be Worth Millions',
    description: 'Turn your authentic stories into income. Write diary entries and books, set your own prices, and keep 80% of earnings.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'OnlyDiary - Personal Storytelling Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OnlyDiary - Your Words Could be Worth Millions',
    description: 'Turn your authentic stories into income. Write diary entries and books, set your own prices, and keep 80% of earnings.',
    images: ['/og-image.jpg'],
    creator: '@onlydiary',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  other: {
    'Permissions-Policy': 'microphone=*',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NavigationProvider>
          <Suspense fallback={null}>
            <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID || ''} />
            <PerformanceMonitor />
          </Suspense>
          <Analytics />
          <Navigation />
          <ConnectionStatus />
          {children}
          <Footer />
          <PushNotificationPrompt />
        </NavigationProvider>
      </body>
    </html>
  );
}
