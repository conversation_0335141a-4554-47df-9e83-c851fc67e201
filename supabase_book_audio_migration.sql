-- Day 1 Badge System Migration
-- Run this in Supabase SQL Editor BEFORE launching to creators

-- 1. Add Day 1 badge fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS has_day1_badge BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS signup_number INTEGER;

-- 2. <PERSON><PERSON> function to automatically assign Day 1 badges to first 500 signups
CREATE OR REPLACE FUNCTION assign_day1_badge()
RETURNS TRIGGER AS $$
DECLARE
    current_signup_count INTEGER;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM users WHERE id != NEW.id;

    -- Assign signup number
    NEW.signup_number := current_signup_count + 1;

    -- Assign Day 1 badge if within first 500 signups
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
    ELSE
        NEW.has_day1_badge := FALSE;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. <PERSON>reate trigger to run the function on user signup
DROP TRIGGER IF EXISTS assign_day1_badge_trigger ON users;
CREATE TRIGGER assign_day1_badge_trigger
    BEFORE INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION assign_day1_badge();

-- 4. Create index for performance
CREATE INDEX IF NOT EXISTS idx_users_signup_number ON users(signup_number);
CREATE INDEX IF NOT EXISTS idx_users_day1_badge ON users(has_day1_badge) WHERE has_day1_badge = TRUE;

-- 5. Create function to get Day 1 badge holders (for admin/analytics)
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.created_at
    FROM users u
    WHERE u.has_day1_badge = TRUE
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant permissions
GRANT EXECUTE ON FUNCTION get_day1_badge_holders() TO authenticated;

-- 7. Update RLS policy for users table to include new fields
DROP POLICY IF EXISTS "Users can view public profiles" ON users;
CREATE POLICY "Users can view public profiles" ON users
    FOR SELECT USING (true);

-- 8. Create function to reset signup count (USE CAREFULLY - ONLY FOR TESTING)
CREATE OR REPLACE FUNCTION reset_signup_count_and_badges()
RETURNS void AS $$
BEGIN
    -- WARNING: This will reset all signup numbers and Day 1 badges
    -- Only use this before your official launch
    UPDATE users SET
        signup_number = NULL,
        has_day1_badge = FALSE;

    -- Reset the sequence by updating signup numbers based on created_at
    WITH numbered_users AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at ASC) as new_signup_number
        FROM users
    )
    UPDATE users
    SET
        signup_number = numbered_users.new_signup_number,
        has_day1_badge = CASE WHEN numbered_users.new_signup_number <= 500 THEN TRUE ELSE FALSE END
    FROM numbered_users
    WHERE users.id = numbered_users.id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Grant permission for reset function (restrict to admin use)
GRANT EXECUTE ON FUNCTION reset_signup_count_and_badges() TO authenticated;

-- Book Audio Discussions Migration (keeping original content)
-- 1. Create book_audio_posts table
CREATE TABLE book_audio_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL,
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL DEFAULT 0,
    waveform_data JSONB,
    
    -- Position tracking within chapter
    chapter_position INTEGER DEFAULT 0,
    page_context TEXT,
    
    -- Content
    description TEXT CHECK (LENGTH(description) <= 50),
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create book_audio_replies table
CREATE TABLE book_audio_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL,
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL DEFAULT 0,
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create book_audio_loves table
CREATE TABLE book_audio_loves (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE,
    book_audio_reply_id UUID REFERENCES book_audio_replies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure user can only love a post OR reply, not both
    CHECK (
        (book_audio_post_id IS NOT NULL AND book_audio_reply_id IS NULL) OR
        (book_audio_post_id IS NULL AND book_audio_reply_id IS NOT NULL)
    ),
    -- Ensure unique love per user per post/reply
    UNIQUE(user_id, book_audio_post_id),
    UNIQUE(user_id, book_audio_reply_id)
);

-- 4. Create indexes for performance
CREATE INDEX idx_book_audio_posts_project_chapter ON book_audio_posts(project_id, chapter_id);
CREATE INDEX idx_book_audio_posts_user ON book_audio_posts(user_id);
CREATE INDEX idx_book_audio_posts_created_at ON book_audio_posts(created_at DESC);
CREATE INDEX idx_book_audio_replies_post ON book_audio_replies(book_audio_post_id);
CREATE INDEX idx_book_audio_replies_user ON book_audio_replies(user_id);

-- 5. Enable RLS
ALTER TABLE book_audio_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_audio_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_audio_loves ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies
-- Users can view audio posts for books they have access to
CREATE POLICY "Users can view book audio posts they have access to" ON book_audio_posts
    FOR SELECT USING (
        project_id IN (
            SELECT p.id FROM projects p 
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

-- Users can create audio posts for books they have access to
CREATE POLICY "Users can create book audio posts for accessible books" ON book_audio_posts
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        project_id IN (
            SELECT p.id FROM projects p 
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

-- Users can update their own posts
CREATE POLICY "Users can update their own book audio posts" ON book_audio_posts
    FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own posts
CREATE POLICY "Users can delete their own book audio posts" ON book_audio_posts
    FOR DELETE USING (user_id = auth.uid());

-- Similar policies for replies
CREATE POLICY "Users can view book audio replies for accessible books" ON book_audio_replies
    FOR SELECT USING (
        book_audio_post_id IN (
            SELECT bap.id FROM book_audio_posts bap
            JOIN projects p ON p.id = bap.project_id
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

CREATE POLICY "Users can create book audio replies for accessible books" ON book_audio_replies
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        book_audio_post_id IN (
            SELECT bap.id FROM book_audio_posts bap
            JOIN projects p ON p.id = bap.project_id
            LEFT JOIN book_purchases bp ON bp.project_id = p.id AND bp.user_id = auth.uid()
            WHERE p.user_id = auth.uid() 
               OR bp.id IS NOT NULL 
               OR p.price_amount = 0
        )
    );

CREATE POLICY "Users can update their own book audio replies" ON book_audio_replies
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own book audio replies" ON book_audio_replies
    FOR DELETE USING (user_id = auth.uid());

-- Love policies
CREATE POLICY "Users can view book audio loves" ON book_audio_loves
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own book audio loves" ON book_audio_loves
    FOR ALL USING (user_id = auth.uid());

-- 7. Create triggers to update reply counts
CREATE OR REPLACE FUNCTION update_book_audio_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE book_audio_posts
        SET reply_count = reply_count + 1
        WHERE id = NEW.book_audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE book_audio_posts
        SET reply_count = reply_count - 1
        WHERE id = OLD.book_audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_reply_count_trigger
    AFTER INSERT OR DELETE ON book_audio_replies
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_reply_count();

-- 8. Create triggers to update love counts
CREATE OR REPLACE FUNCTION update_book_audio_love_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_post_id;
        ELSIF NEW.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.book_audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_post_id;
        ELSIF OLD.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies
            SET love_count = love_count - 1
            WHERE id = OLD.book_audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_love_count_trigger
    AFTER INSERT OR DELETE ON book_audio_loves
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_love_count();
