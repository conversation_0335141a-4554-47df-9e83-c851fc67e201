-- OnlyAudio Feature Migration
-- Creates audio posts and replies with same reaction system as diary entries

-- 1. Create audio_posts table
CREATE TABLE audio_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL, -- R2 storage key
    description TEXT CHECK (LENGTH(description) <= 50),
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL,
    waveform_data JSONB, -- For visual waveform display
    love_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create audio_replies table
CREATE TABLE audio_replies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audio_post_id UUID REFERENCES audio_posts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL, -- R2 storage key
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL,
    love_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create audio_loves table (similar to existing loves table)
CREATE TABLE audio_loves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    audio_post_id UUID REFERENCES audio_posts(id) ON DELETE CASCADE,
    audio_reply_id UUID REFERENCES audio_replies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure user can only love a post OR reply, not both
    CHECK (
        (audio_post_id IS NOT NULL AND audio_reply_id IS NULL) OR
        (audio_post_id IS NULL AND audio_reply_id IS NOT NULL)
    ),
    -- Ensure unique love per user per post/reply
    UNIQUE(user_id, audio_post_id),
    UNIQUE(user_id, audio_reply_id)
);

-- 4. Create audio_reactions table (similar to existing reactions table)
CREATE TABLE audio_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    audio_post_id UUID REFERENCES audio_posts(id) ON DELETE CASCADE,
    audio_reply_id UUID REFERENCES audio_replies(id) ON DELETE CASCADE,
    reaction_type VARCHAR(20) NOT NULL, -- 'like', 'love', 'laugh', 'wow', 'sad', 'angry'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure reaction is for post OR reply, not both
    CHECK (
        (audio_post_id IS NOT NULL AND audio_reply_id IS NULL) OR
        (audio_post_id IS NULL AND audio_reply_id IS NOT NULL)
    ),
    -- One reaction type per user per post/reply
    UNIQUE(user_id, audio_post_id, reaction_type),
    UNIQUE(user_id, audio_reply_id, reaction_type)
);

-- 5. Create indexes for performance
CREATE INDEX idx_audio_posts_user_id ON audio_posts(user_id);
CREATE INDEX idx_audio_posts_created_at ON audio_posts(created_at DESC);
CREATE INDEX idx_audio_replies_post_id ON audio_replies(audio_post_id);
CREATE INDEX idx_audio_replies_user_id ON audio_replies(user_id);
CREATE INDEX idx_audio_loves_post_id ON audio_loves(audio_post_id);
CREATE INDEX idx_audio_loves_reply_id ON audio_loves(audio_reply_id);
CREATE INDEX idx_audio_reactions_post_id ON audio_reactions(audio_post_id);
CREATE INDEX idx_audio_reactions_reply_id ON audio_reactions(audio_reply_id);

-- 6. Create triggers to update love counts
CREATE OR REPLACE FUNCTION update_audio_love_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.audio_post_id IS NOT NULL THEN
            UPDATE audio_posts
            SET love_count = love_count + 1
            WHERE id = NEW.audio_post_id;
        ELSIF NEW.audio_reply_id IS NOT NULL THEN
            UPDATE audio_replies
            SET love_count = love_count + 1
            WHERE id = NEW.audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.audio_post_id IS NOT NULL THEN
            UPDATE audio_posts
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_post_id;
        ELSIF OLD.audio_reply_id IS NOT NULL THEN
            UPDATE audio_replies
            SET love_count = GREATEST(love_count - 1, 0)
            WHERE id = OLD.audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger to update reply counts
CREATE OR REPLACE FUNCTION update_audio_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE audio_posts
        SET reply_count = reply_count + 1
        WHERE id = NEW.audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE audio_posts
        SET reply_count = GREATEST(reply_count - 1, 0)
        WHERE id = OLD.audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 8. Create triggers
CREATE TRIGGER update_audio_love_count_trigger
    AFTER INSERT OR DELETE ON audio_loves
    FOR EACH ROW EXECUTE FUNCTION update_audio_love_count();

CREATE TRIGGER update_audio_reply_count_trigger
    AFTER INSERT OR DELETE ON audio_replies
    FOR EACH ROW EXECUTE FUNCTION update_audio_reply_count();

-- 9. Enable RLS (Row Level Security)
ALTER TABLE audio_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_loves ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_reactions ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies
-- Audio posts - everyone can read, only authenticated users can create
CREATE POLICY "Audio posts are viewable by everyone" ON audio_posts
    FOR SELECT USING (true);

CREATE POLICY "Users can create their own audio posts" ON audio_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own audio posts" ON audio_posts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own audio posts" ON audio_posts
    FOR DELETE USING (auth.uid() = user_id);

-- Audio replies - everyone can read, only authenticated users can create
CREATE POLICY "Audio replies are viewable by everyone" ON audio_replies
    FOR SELECT USING (true);

CREATE POLICY "Users can create audio replies" ON audio_replies
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own audio replies" ON audio_replies
    FOR DELETE USING (auth.uid() = user_id);

-- Audio loves - users can manage their own loves
CREATE POLICY "Users can view all audio loves" ON audio_loves
    FOR SELECT USING (true);

CREATE POLICY "Users can create their own audio loves" ON audio_loves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own audio loves" ON audio_loves
    FOR DELETE USING (auth.uid() = user_id);

-- Audio reactions - users can manage their own reactions
CREATE POLICY "Users can view all audio reactions" ON audio_reactions
    FOR SELECT USING (true);

CREATE POLICY "Users can create their own audio reactions" ON audio_reactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own audio reactions" ON audio_reactions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own audio reactions" ON audio_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- Success message
SELECT 'OnlyAudio tables created successfully!' as status;
