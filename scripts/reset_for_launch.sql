-- LAUNCH RESET SCRIPT
-- Run this in Supabase SQL Editor to clean test data before launch
-- WARNING: This will delete ALL test data - use carefully!

-- 1. Reset Day 1 badges and signup numbers
SELECT reset_signup_count_and_badges();

-- 2. Optional: Clean up test data (uncomment sections you want to clean)

-- Clean test diary entries (be careful!)
-- DELETE FROM diary_entries WHERE user_id IN (
--     SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'
-- );

-- Clean test photos
-- DELETE FROM photos WHERE diary_entry_id IN (
--     SELECT id FROM diary_entries WHERE user_id IN (
--         SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'
--     )
-- );

-- Clean test comments
-- DELETE FROM comments WHERE user_id IN (
--     SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'
-- );

-- Clean test subscriptions
-- DELETE FROM subscriptions WHERE subscriber_id IN (
--     SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'
-- ) OR writer_id IN (
--     SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'
-- );

-- Clean test users (be very careful!)
-- DELETE FROM users WHERE email LIKE '%test%' OR email LIKE '%example%';

-- 3. Reset auto-increment sequences if needed
-- This ensures clean numbering for new signups
-- ALTER SEQUENCE IF EXISTS users_signup_number_seq RESTART WITH 1;

-- 4. Verify the reset worked
SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE has_day1_badge = TRUE) as day1_badge_holders,
    MIN(signup_number) as min_signup_number,
    MAX(signup_number) as max_signup_number
FROM users;

-- 5. Show current Day 1 badge holders (should be empty or only real users)
SELECT * FROM get_day1_badge_holders() LIMIT 10;
