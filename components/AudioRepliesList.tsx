'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { AudioReply } from './AudioReply'

interface AudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  created_at: string
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
}

interface AudioRepliesListProps {
  postId: string
  currentUserId?: string
  onReplyCountChange?: (count: number) => void
}

export function AudioRepliesList({ 
  postId, 
  currentUserId, 
  onReplyCountChange 
}: AudioRepliesListProps) {
  const [replies, setReplies] = useState<AudioReply[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  const fetchReplies = async () => {
    try {
      setLoading(true)
      setError(null)

      const { data: repliesData, error: repliesError } = await supabase
        .from('audio_replies')
        .select(`
          id,
          user_id,
          audio_url,
          duration_seconds,
          love_count,
          created_at,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url,
            has_day1_badge,
            signup_number
          )
        `)
        .eq('audio_post_id', postId)
        .order('created_at', { ascending: true })

      if (repliesError) {
        throw repliesError
      }

      setReplies(repliesData || [])
      onReplyCountChange?.(repliesData?.length || 0)
    } catch (error) {
      console.error('Error fetching audio replies:', error)
      setError('Failed to load replies')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchReplies()
  }, [postId])

  const handleLoveReply = async (replyId: string) => {
    if (!currentUserId) return

    try {
      // Check if already loved
      const { data: existingLove } = await supabase
        .from('audio_loves')
        .select('id')
        .eq('user_id', currentUserId)
        .eq('audio_reply_id', replyId)
        .single()

      if (existingLove) {
        // Remove love
        await supabase
          .from('audio_loves')
          .delete()
          .eq('user_id', currentUserId)
          .eq('audio_reply_id', replyId)

        // Update love count
        await supabase
          .from('audio_replies')
          .update({ love_count: supabase.raw('love_count - 1') })
          .eq('id', replyId)
      } else {
        // Add love
        await supabase
          .from('audio_loves')
          .insert({
            user_id: currentUserId,
            audio_reply_id: replyId
          })

        // Update love count
        await supabase
          .from('audio_replies')
          .update({ love_count: supabase.raw('love_count + 1') })
          .eq('id', replyId)
      }

      // Refresh replies to get updated counts
      fetchReplies()
    } catch (error) {
      console.error('Error loving reply:', error)
    }
  }

  const handleReplyToReply = (replyId: string) => {
    // TODO: Implement nested replies
    console.log('Reply to reply:', replyId)
  }

  if (loading) {
    return (
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-center py-6 sm:py-8">
          <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-2 text-sm sm:text-base text-gray-600">Loading replies...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="text-center py-6 sm:py-8">
          <p className="text-red-600 mb-4 text-sm sm:text-base">{error}</p>
          <button
            onClick={fetchReplies}
            className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors min-h-[44px] text-sm sm:text-base"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (replies.length === 0) {
    return (
      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs sm:text-sm text-gray-500 text-center py-4">
          No audio replies yet. Be the first to reply! 🎤
        </p>
      </div>
    )
  }

  return (
    <div className="mt-4 pt-4 border-t border-gray-100 px-4">
      <h4 className="text-sm font-medium text-gray-900 mb-4">
        🎵 Audio Replies ({replies.length})
      </h4>

      <div className="space-y-3">
        {replies.map((reply) => (
          <AudioReply
            key={reply.id}
            reply={reply}
            currentUserId={currentUserId}
            onLove={handleLoveReply}
            onReply={handleReplyToReply}
            level={0}
          />
        ))}
      </div>
    </div>
  )
}
