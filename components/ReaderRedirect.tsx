'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase/client'

export function UserRedirect() {
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const checkUserAndRedirect = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          // Get user profile to check role
          const { data: profile } = await supabase
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single()

          // Redirect all users to timeline (unified experience)
          if (profile?.role === 'admin') {
            router.push('/dashboard')
          } else if (profile?.role === 'user') {
            router.push('/timeline')
          }
        }
      } catch (error) {
        // Ignore errors, just stay on homepage
        console.log('User check failed:', error)
      }
    }

    checkUserAndRedirect()
  }, [router, supabase])

  return null // This component doesn't render anything
}
