'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { InvitePrompt } from './InvitePrompt'

interface SmartInviteModalProps {
  userId: string
  userName?: string
  trigger?: 'first_login' | 'first_post'
}

export function SmartInviteModal({ userId, userName, trigger = 'first_login' }: SmartInviteModalProps) {
  const [showModal, setShowModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkShouldShowModal()
  }, [userId, trigger])

  const checkShouldShowModal = async () => {
    try {
      setLoading(true)

      // Check user's invite modal history
      const { data: user, error } = await supabase
        .from('users')
        .select('invite_modal_shown_first_login, invite_modal_shown_first_post, created_at')
        .eq('id', userId)
        .single()

      if (error || !user) {
        console.error('Error fetching user invite modal status:', error)
        setLoading(false)
        return
      }

      let shouldShow = false

      if (trigger === 'first_login') {
        // Show on first login if never shown before
        shouldShow = !user.invite_modal_shown_first_login
      } else if (trigger === 'first_post') {
        // Show after first post if:
        // 1. First login modal was ignored (shown but user didn't interact)
        // 2. First post modal hasn't been shown yet
        shouldShow = user.invite_modal_shown_first_login && !user.invite_modal_shown_first_post
      }

      if (shouldShow) {
        // Small delay for better UX
        setTimeout(() => {
          setShowModal(true)
          setLoading(false)
        }, 1500)
      } else {
        setLoading(false)
      }
    } catch (error) {
      console.error('Error checking invite modal status:', error)
      setLoading(false)
    }
  }

  const handleModalClose = async () => {
    setShowModal(false)
    
    // Track that modal was shown for this trigger
    try {
      const updateData = trigger === 'first_login' 
        ? { invite_modal_shown_first_login: true }
        : { invite_modal_shown_first_post: true }

      await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating invite modal status:', error)
    }
  }

  const handleInviteAction = async () => {
    // User took action (invited friends or copied link)
    // Mark both triggers as complete so modal never shows again
    try {
      await supabase
        .from('users')
        .update({
          invite_modal_shown_first_login: true,
          invite_modal_shown_first_post: true
        })
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating invite modal status:', error)
    }
    
    setShowModal(false)
  }

  if (loading || !showModal) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 max-w-md w-full relative">
        {/* Close button */}
        <button
          onClick={handleModalClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">👥</span>
          </div>
          <h3 className="text-xl font-serif text-gray-800 mb-2">
            OnlyDiary is better with friends
          </h3>
          <p className="text-gray-600">
            Invite friends to read your stories and discover theirs
          </p>
        </div>

        <div className="space-y-3">
          <button
            onClick={handleInviteAction}
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Invite Friends
          </button>
          
          <button
            onClick={handleInviteAction}
            className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Link
          </button>
        </div>

        <div className="mt-4 text-center">
          <button
            onClick={handleModalClose}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            Maybe later
          </button>
        </div>
      </div>
    </div>
  )
}
