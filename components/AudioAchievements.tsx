'use client'

interface Achievement {
  achievement_type: string
  play_count_achieved: number
  achieved_at: string
}

interface AudioAchievementsProps {
  achievements: Achievement[]
  playCount: number
  className?: string
}

export function AudioAchievements({ achievements, playCount, className = '' }: AudioAchievementsProps) {
  if (!achievements || achievements.length === 0) {
    return null
  }

  const getAchievementInfo = (type: string) => {
    switch (type) {
      case 'gold':
        return {
          icon: '🥇',
          name: 'Gold Plaque',
          color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
          description: '500K+ plays'
        }
      case 'platinum':
        return {
          icon: '🥈',
          name: 'Platinum Plaque',
          color: 'text-gray-600 bg-gray-50 border-gray-300',
          description: '1M+ plays'
        }
      case 'double_platinum':
        return {
          icon: '🥈🥈',
          name: 'Double Platinum',
          color: 'text-gray-700 bg-gray-100 border-gray-400',
          description: '2M+ plays'
        }
      case 'diamond':
        return {
          icon: '💎',
          name: 'Diamond Plaque',
          color: 'text-blue-600 bg-blue-50 border-blue-200',
          description: '10M+ plays'
        }
      default:
        return {
          icon: '🏆',
          name: 'Achievement',
          color: 'text-gray-600 bg-gray-50 border-gray-200',
          description: 'Special milestone'
        }
    }
  }

  const formatPlayCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  // Sort achievements by play count (highest first)
  const sortedAchievements = [...achievements].sort((a, b) => b.play_count_achieved - a.play_count_achieved)
  
  // Show only the highest achievement
  const topAchievement = sortedAchievements[0]
  const achievementInfo = getAchievementInfo(topAchievement.achievement_type)

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`flex items-center gap-1.5 px-2 py-1 rounded-full border text-xs font-medium ${achievementInfo.color}`}>
        <span className="text-sm">{achievementInfo.icon}</span>
        <span>{achievementInfo.name}</span>
      </div>
      
      <div className="text-xs text-gray-500">
        {formatPlayCount(playCount)} plays
      </div>
      
      {/* Show all achievements on hover */}
      {sortedAchievements.length > 1 && (
        <div className="relative group">
          <button className="text-xs text-gray-400 hover:text-gray-600">
            +{sortedAchievements.length - 1} more
          </button>
          
          <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block z-10">
            <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-48">
              <div className="text-xs font-medium text-gray-700 mb-2">All Achievements:</div>
              <div className="space-y-1">
                {sortedAchievements.map((achievement, index) => {
                  const info = getAchievementInfo(achievement.achievement_type)
                  return (
                    <div key={index} className="flex items-center gap-2 text-xs">
                      <span>{info.icon}</span>
                      <span className="font-medium">{info.name}</span>
                      <span className="text-gray-500">
                        ({formatPlayCount(achievement.play_count_achieved)} plays)
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Component to show next milestone progress
export function NextMilestone({ playCount }: { playCount: number }) {
  const getMilestones = () => [
    { threshold: 500000, name: 'Gold Plaque', icon: '🥇' },
    { threshold: 1000000, name: 'Platinum Plaque', icon: '🥈' },
    { threshold: 2000000, name: 'Double Platinum', icon: '🥈🥈' },
    { threshold: 10000000, name: 'Diamond Plaque', icon: '💎' },
  ]

  const milestones = getMilestones()
  const nextMilestone = milestones.find(m => playCount < m.threshold)
  
  if (!nextMilestone) {
    return null // Already achieved all milestones
  }

  const progress = (playCount / nextMilestone.threshold) * 100
  const remaining = nextMilestone.threshold - playCount

  const formatCount = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`
    return count.toString()
  }

  return (
    <div className="bg-gray-50 rounded-lg p-3 mt-2">
      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
        <span>Next: {nextMilestone.icon} {nextMilestone.name}</span>
        <span>{formatCount(remaining)} plays to go</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-1.5">
        <div 
          className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
      <div className="text-xs text-gray-500 mt-1">
        {formatCount(playCount)} / {formatCount(nextMilestone.threshold)} plays ({progress.toFixed(1)}%)
      </div>
    </div>
  )
}
