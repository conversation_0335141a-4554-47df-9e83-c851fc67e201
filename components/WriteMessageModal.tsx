'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface WriteMessageModalProps {
  isOpen: boolean
  onClose: () => void
  recipientId: string
  recipientName: string
}

export function WriteMessageModal({ isOpen, onClose, recipientId, recipientName }: WriteMessageModalProps) {
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [sending, setSending] = useState(false)
  const supabase = createSupabaseClient()

  const handleSend = async () => {
    if (!message.trim()) return

    setSending(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        alert('Please sign in to send messages')
        return
      }

      // Send the message
      const { error: messageError } = await supabase
        .from('direct_messages')
        .insert({
          sender_id: user.id,
          recipient_id: recipientId,
          subject: subject.trim() || 'New Message',
          body: message.trim()
        })

      if (messageError) {
        console.error('Error sending message:', messageError)
        alert('Failed to send message: ' + messageError.message)
        return
      }

      // Create notification for recipient
      const { data: senderProfile } = await supabase
        .from('users')
        .select('name')
        .eq('id', user.id)
        .single()

      await supabase
        .from('notifications')
        .insert({
          user_id: recipientId,
          type: 'message',
          title: 'New Message',
          body: `${senderProfile?.name || 'Someone'} sent you a message: "${subject || 'New Message'}"`,
          data: {
            sender_id: user.id,
            sender_name: senderProfile?.name,
            message_preview: message.substring(0, 100)
          }
        })

      // Reset form and close
      setSubject('')
      setMessage('')
      onClose()
      alert('Message sent successfully!')

    } catch (error) {
      console.error('Error sending message:', error)
      alert('Failed to send message')
    } finally {
      setSending(false)
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden">
          
          {/* Header */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-serif text-gray-800">
                Write to {recipientName}
              </h2>
              <button 
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 text-xl"
              >
                ×
              </button>
            </div>
            <p className="text-gray-500 text-sm mt-1">
              Send a private message to this writer
            </p>
          </div>

          {/* Form */}
          <div className="p-6 space-y-4">
            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject (optional)
              </label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="What's this message about?"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 outline-none"
                maxLength={255}
              />
            </div>

            {/* Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message *
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Write your message here..."
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 outline-none resize-none"
                maxLength={2000}
              />
              <div className="text-xs text-gray-500 mt-1">
                {message.length}/2000 characters
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-100 flex gap-3 justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
              disabled={sending}
            >
              Cancel
            </button>
            <button
              onClick={handleSend}
              disabled={!message.trim() || sending}
              className="bg-amber-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-amber-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {sending ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Sending...
                </>
              ) : (
                <>
                  ✉️ Send Message
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
