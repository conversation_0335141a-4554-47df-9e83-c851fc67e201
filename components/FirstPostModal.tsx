'use client'

import { useEffect } from 'react'
import { useFirstPostModal } from '@/hooks/useFirstPostModal'

interface FirstPostModalProps {
  userId: string
  userName?: string
  onInviteAction?: () => void
}

export function FirstPostModal({ userId, userName, onInviteAction }: FirstPostModalProps) {
  const { showModal, checkAndShowFirstPostModal, handleModalClose, handleInviteAction } = useFirstPostModal(userId, userName)

  // Listen for the custom event triggered after publishing content
  useEffect(() => {
    const handleCheckModal = (event: CustomEvent) => {
      if (event.detail?.userId === userId) {
        checkAndShowFirstPostModal()
      }
    }

    window.addEventListener('checkFirstPostModal', handleCheckModal as EventListener)
    return () => {
      window.removeEventListener('checkFirstPostModal', handleCheckModal as EventListener)
    }
  }, [userId, checkAndShowFirstPostModal])

  const handleInvite = async () => {
    // Generate invite link and open SMS
    try {
      const inviteCode = `${userName?.replace(/\s+/g, '') || 'friend'}${Date.now()}`
      const inviteLink = `https://onlydiary.app/invite/${inviteCode}`
      const message = `Hey! I just published my first story on OnlyDiary. Check it out: ${inviteLink}`
      
      // Open SMS with pre-filled message
      window.open(`sms:?body=${encodeURIComponent(message)}`)
      
      // Call the action handler
      await handleInviteAction()
      onInviteAction?.()
    } catch (error) {
      console.error('Error sending invite:', error)
    }
  }

  const handleCopyLink = async () => {
    // Generate invite link and copy to clipboard
    try {
      const inviteCode = `${userName?.replace(/\s+/g, '') || 'friend'}${Date.now()}`
      const inviteLink = `https://onlydiary.app/invite/${inviteCode}`
      const message = `Hey! I just published my first story on OnlyDiary. Check it out: ${inviteLink}`
      
      await navigator.clipboard.writeText(message)
      alert('Invite message copied to clipboard!')
      
      // Call the action handler
      await handleInviteAction()
      onInviteAction?.()
    } catch (error) {
      console.error('Error copying invite:', error)
      alert('Failed to copy invite link')
    }
  }

  if (!showModal) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 max-w-md w-full relative">
        {/* Close button */}
        <button
          onClick={handleModalClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">🎉</span>
          </div>
          <h3 className="text-xl font-serif text-gray-800 mb-2">
            Great job on your first post!
          </h3>
          <p className="text-gray-600">
            OnlyDiary is better with friends. Invite them to read your stories and discover theirs.
          </p>
        </div>

        <div className="space-y-3">
          <button
            onClick={handleInvite}
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            Invite Friends
          </button>
          
          <button
            onClick={handleCopyLink}
            className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Link
          </button>
        </div>

        <div className="mt-4 text-center">
          <button
            onClick={handleModalClose}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            Maybe later
          </button>
        </div>
      </div>
    </div>
  )
}
