"use client"

import { <PERSON>, Star, Zap, Shield, Award, ExternalLink } from "lucide-react"
import { useState, useRef, useEffect } from "react"
import Link from "next/link"

interface Day1BadgeProps {
  signupNumber?: number
  badgeTier?: string
  className?: string
  size?: "sm" | "md" | "lg"
  showTooltip?: boolean
  clickable?: boolean
}

interface BadgeConfig {
  name: string
  gradient: string
  icon: any
  iconColor: string
  range: string
  description: string
}

export function Day1Badge({
  signupNumber,
  badgeTier,
  className = "",
  size = "md",
  showTooltip = true,
  clickable = true
}: Day1BadgeProps) {
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  // Auto-determine badge tier from signup number if not provided
  const determineBadgeTier = (signupNum?: number): string => {
    if (!signupNum) return "day1"
    if (signupNum <= 500) return "day1"
    if (signupNum <= 1000) return "pioneer"
    if (signupNum <= 2500) return "founder"
    if (signupNum <= 5000) return "early_adopter"
    if (signupNum <= 10000) return "charter_member"
    return "day1"
  }

  const actualBadgeTier = badgeTier || determineBadgeTier(signupNumber)
  const sizeClasses = {
    sm: "text-xs px-1.5 py-0.5 sm:px-2 sm:py-1",
    md: "text-xs sm:text-sm px-2 py-1 sm:px-2.5 sm:py-1",
    lg: "text-sm sm:text-base px-2.5 py-1 sm:px-3 sm:py-1.5"
  }

  const iconSizes = {
    sm: 10,
    md: 12,
    lg: 14
  }

  // Badge configurations for each tier
  const getBadgeConfig = (tier: string): BadgeConfig => {
    const configs: Record<string, BadgeConfig> = {
      day1: {
        name: "Day 1",
        gradient: "from-purple-600 to-pink-600",
        icon: Crown,
        iconColor: "text-yellow-300",
        range: "1-500",
        description: "One of the first 500 OnlyDiary members"
      },
      pioneer: {
        name: "Pioneer",
        gradient: "from-yellow-500 to-orange-500",
        icon: Star,
        iconColor: "text-white",
        range: "501-1,000",
        description: "Pioneer member of OnlyDiary"
      },
      founder: {
        name: "Founder",
        gradient: "from-gray-400 to-gray-600",
        icon: Zap,
        iconColor: "text-blue-200",
        range: "1,001-2,500",
        description: "Founding member of OnlyDiary"
      },
      early_adopter: {
        name: "Early Adopter",
        gradient: "from-amber-600 to-amber-800",
        icon: Star,
        iconColor: "text-amber-200",
        range: "2,501-5,000",
        description: "Early adopter of OnlyDiary"
      },
      charter_member: {
        name: "Charter",
        gradient: "from-blue-500 to-blue-700",
        icon: Crown,
        iconColor: "text-blue-200",
        range: "5,001-10,000",
        description: "Charter member of OnlyDiary"
      }
    }
    return configs[tier] || configs.day1
  }

  const config = getBadgeConfig(actualBadgeTier)
  const IconComponent = config.icon
  const isDay1 = actualBadgeTier === "day1"

  // Crown verification badges - different colors for each tier
  const getCrownStyle = (tier: string) => {
    const styles = {
      day1: {
        bg: "bg-purple-50",
        border: "border-purple-200",
        crown: "text-purple-600",
        hover: "hover:bg-purple-100 hover:border-purple-300"
      },
      pioneer: {
        bg: "bg-yellow-50",
        border: "border-yellow-200",
        crown: "text-yellow-600",
        hover: "hover:bg-yellow-100 hover:border-yellow-300"
      },
      founder: {
        bg: "bg-gray-50",
        border: "border-gray-300",
        crown: "text-gray-600",
        hover: "hover:bg-gray-100 hover:border-gray-400"
      },
      early_adopter: {
        bg: "bg-amber-50",
        border: "border-amber-200",
        crown: "text-amber-700",
        hover: "hover:bg-amber-100 hover:border-amber-300"
      },
      charter_member: {
        bg: "bg-blue-50",
        border: "border-blue-200",
        crown: "text-blue-600",
        hover: "hover:bg-blue-100 hover:border-blue-300"
      }
    }
    return styles[tier as keyof typeof styles] || styles.day1
  }

  const crownStyle = getCrownStyle(actualBadgeTier)

  const BadgeContent = () => (
    <div
      className={`
        inline-flex items-center justify-center
        ${crownStyle.bg} ${crownStyle.border} ${crownStyle.hover}
        border rounded-full
        ${size === "sm" ? "w-5 h-5" : size === "md" ? "w-6 h-6" : "w-7 h-7"}
        ${clickable ? 'cursor-pointer' : ''}
        ${className}
      `}
      title={showTooltip && !clickable ? `${config.name} Member - ${config.description}` : undefined}
      onClick={clickable ? () => setShowDropdown(!showDropdown) : undefined}
    >
      <IconComponent size={size === "sm" ? 14 : size === "md" ? 16 : 18} className={crownStyle.crown} />
    </div>
  )

  if (!clickable) {
    return <BadgeContent />
  }

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <BadgeContent />

      {/* Clean Info Dropdown */}
      {showDropdown && (
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50">
          <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-64">
            {/* Arrow */}
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white border-l border-t border-gray-200 rotate-45"></div>

            {/* Header */}
            <div className="text-center mb-3">
              <div className={`inline-flex p-2 rounded-full ${crownStyle.bg} ${crownStyle.border} border mb-2`}>
                <IconComponent size={18} className={crownStyle.crown} />
              </div>
              <h3 className="font-semibold text-gray-900 text-sm">{config.name} Member</h3>
              <p className="text-xs text-gray-500">#{signupNumber} • {config.range}</p>
            </div>

            {/* Description */}
            <p className="text-gray-600 text-xs text-center mb-3 leading-relaxed">
              {config.description}
            </p>

            {/* Learn More */}
            <Link
              href="/crown-system"
              className="block text-center text-purple-600 hover:text-purple-700 text-xs font-medium"
              onClick={() => setShowDropdown(false)}
            >
              Learn more →
            </Link>
          </div>
        </div>
      )}
    </div>
  )


}

// Alternative badge designs
export function Day1BadgeMinimal({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-purple-100 text-purple-800 border border-purple-300
        font-medium text-xs rounded-full px-2 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Member #${signupNumber}`}
    >
      <Star size={12} />
      Day 1
    </div>
  )
}

export function Day1BadgeGold({ signupNumber, className = "" }: { signupNumber?: number, className?: string }) {
  return (
    <div
      className={`
        bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900
        font-bold text-sm border-0 shadow-md rounded-full px-3 py-1
        flex items-center gap-1
        ${className}
      `}
      title={`Day 1 Founder #${signupNumber} - One of the first 500`}
    >
      <Zap size={14} className="text-yellow-800" />
      Founder
      {signupNumber && <span>#{signupNumber}</span>}
    </div>
  )
}

// Hook to check if user has Day 1 badge
export function useDay1Badge(user: any) {
  return {
    hasDay1Badge: user?.has_day1_badge || false,
    signupNumber: user?.signup_number || null,
    isEarlyAdopter: user?.signup_number && user.signup_number <= 500
  }
}
