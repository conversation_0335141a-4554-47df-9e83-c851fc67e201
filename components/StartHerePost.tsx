"use client"

import Link from "next/link"
import Image from "next/image"
import { VideoThumbnail } from '@/components/VideoThumbnail'

interface StartHerePostProps {
  entry: {
    id: string
    title: string
    body_md: string
    created_at: string
    photos?: Array<{ url: string }>
    videos?: Array<{ r2_public_url: string; title: string }>
  }
  writerName: string
  writerAvatar?: string | null
}

export function StartHerePost({ entry, writerName, writerAvatar }: StartHerePostProps) {
  // Get preview text (first 150 characters)
  const cleanText = entry.body_md
    .replace(/[#*`_~]/g, '') // Remove markdown formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces

  const previewText = cleanText.substring(0, 150).trim()
  const totalWords = cleanText.split(/\s+/).length
  const previewWords = previewText.split(/\s+/).length
  const remainingWords = totalWords - previewWords

  const hasMedia = (entry.photos && entry.photos.length > 0) || (entry.videos && entry.videos.length > 0)

  return (
    <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-200 rounded-xl p-6 mb-8 shadow-sm">
      {/* Pinned Badge */}
      <div className="flex items-center gap-2 mb-4">
        <div className="flex items-center gap-2 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
          </svg>
          📌 Start Here
        </div>
        <span className="text-sm text-gray-500">Pinned by {writerName}</span>
      </div>

      <div className="flex gap-6">
        {/* Content */}
        <div className="flex-1">
          <h2 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
            {entry.title}
          </h2>

          <p className="text-gray-700 mb-4 line-clamp-3">
            {previewText}
            {entry.body_md.length > 150 && `... (+${remainingWords} more words)`}
          </p>

          {/* Writer Info */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
              {writerAvatar ? (
                <Image
                  src={writerAvatar}
                  alt={writerName}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-300 flex items-center justify-center text-xs text-gray-600">
                  {writerName.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-900">{writerName}</p>
              <p className="text-xs text-gray-500">
                {new Date(entry.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Media Indicator */}
          {hasMedia && (
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              {entry.photos && entry.photos.length > 0 && (
                <span className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                  {entry.photos.length} photo{entry.photos.length !== 1 ? 's' : ''}
                </span>
              )}
              {entry.videos && entry.videos.length > 0 && (
                <span className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                  </svg>
                  {entry.videos.length} video{entry.videos.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          )}

          {/* Read Button */}
          <Link
            href={`/d/${entry.id}`}
            className="inline-flex items-center gap-2 bg-yellow-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-yellow-600 transition-colors"
          >
            <span>Start Reading</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>

        {/* Media Preview */}
        {hasMedia && (
          <div className="w-32 h-32 flex-shrink-0">
            {entry.videos && entry.videos.length > 0 ? (
              <VideoThumbnail
                videoUrl={entry.videos[0].r2_public_url}
                alt={entry.title}
                className="w-full h-full rounded-lg overflow-hidden"
                timeInSeconds={1}
                showPlayButton={true}
                playButtonSize="sm"
              />
            ) : entry.photos && entry.photos.length > 0 ? (
              <Image
                src={entry.photos[0].url}
                alt={entry.title}
                width={128}
                height={128}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : null}
          </div>
        )}
      </div>
    </div>
  )
}
