'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { DeleteConfirmModal, useDeleteConfirm } from '@/components/DeleteConfirmModal'
import { AudioPlayer } from '@/components/AudioPlayer'

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  play_count?: number
  created_at: string
  audio_reactions?: Array<{
    id: string
    reaction_type: string
    user?: {
      name: string
      avatar?: string
      profile_picture_url?: string
    }
  }>
  audio_replies?: Array<{
    id: string
    audio_url: string
    duration_seconds: number
    love_count: number
    created_at: string
    user?: {
      name: string
      avatar?: string
      profile_picture_url?: string
    }
  }>
}

interface AudioPostsManagerProps {
  initialPosts: AudioPost[]
  onPostsChange?: (posts: AudioPost[]) => void
}

export function AudioPostsManager({ initialPosts, onPostsChange }: AudioPostsManagerProps) {
  const [posts, setPosts] = useState(initialPosts)
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set())
  const [postToDelete, setPostToDelete] = useState<AudioPost | null>(null)
  const [selectedPosts, setSelectedPosts] = useState<Set<string>>(new Set())
  const [bulkDeleting, setBulkDeleting] = useState(false)
  const supabase = createSupabaseClient()
  const deleteConfirm = useDeleteConfirm()

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = date.toLocaleDateString('en-US', { month: 'short' })
    const day = date.getDate()
    return `${month} ${day}, ${year}`
  }

  // Bulk selection functions
  const toggleSelectPost = (postId: string) => {
    setSelectedPosts(prev => {
      const newSet = new Set(prev)
      if (newSet.has(postId)) {
        newSet.delete(postId)
      } else {
        newSet.add(postId)
      }
      return newSet
    })
  }

  const selectAllPosts = () => {
    setSelectedPosts(new Set(posts.map(post => post.id)))
  }

  const deselectAllPosts = () => {
    setSelectedPosts(new Set())
  }

  // Delete functions
  const deletePost = async (post: AudioPost) => {
    setDeletingIds(prev => new Set(prev).add(post.id))
    
    try {
      const { error } = await supabase
        .from('audio_posts')
        .delete()
        .eq('id', post.id)

      if (error) throw error

      const updatedPosts = posts.filter(p => p.id !== post.id)
      setPosts(updatedPosts)
      onPostsChange?.(updatedPosts)
    } catch (error) {
      console.error('Error deleting audio post:', error)
      alert('Failed to delete audio post. Please try again.')
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(post.id)
        return newSet
      })
    }
  }

  const bulkDeletePosts = async () => {
    if (selectedPosts.size === 0) return

    setBulkDeleting(true)
    
    try {
      const { error } = await supabase
        .from('audio_posts')
        .delete()
        .in('id', Array.from(selectedPosts))

      if (error) throw error

      const updatedPosts = posts.filter(post => !selectedPosts.has(post.id))
      setPosts(updatedPosts)
      setSelectedPosts(new Set())
      onPostsChange?.(updatedPosts)
    } catch (error) {
      console.error('Error bulk deleting audio posts:', error)
      alert('Failed to delete audio posts. Please try again.')
    } finally {
      setBulkDeleting(false)
    }
  }

  const handleDeleteClick = (post: AudioPost) => {
    setPostToDelete(post)
  }

  const confirmDelete = () => {
    if (postToDelete) {
      deletePost(postToDelete)
      setPostToDelete(null)
    }
  }

  const handleBulkDeleteClick = () => {
    deleteConfirm.show({
      title: `Delete ${selectedPosts.size} Audio Posts`,
      message: `Are you sure you want to delete ${selectedPosts.size} audio posts? This action cannot be undone.`,
      onConfirm: bulkDeletePosts
    })
  }

  if (posts.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <div className="text-6xl mb-4">🎵</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Audio Posts Yet</h3>
        <p className="text-gray-600 mb-6">
          You haven't created any audio posts yet. Start sharing your voice!
        </p>
        <a
          href="/audio/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          🎤 Create Audio Post
        </a>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with bulk actions */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Audio Posts ({posts.length})</h2>
        
        {selectedPosts.size > 0 && (
          <div className="flex items-center gap-3">
            <span className="text-sm text-gray-600">
              {selectedPosts.size} selected
            </span>
            <button
              onClick={handleBulkDeleteClick}
              disabled={bulkDeleting}
              className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              {bulkDeleting ? 'Deleting...' : 'Delete Selected'}
            </button>
            <button
              onClick={deselectAllPosts}
              className="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors"
            >
              Deselect All
            </button>
          </div>
        )}
        
        {selectedPosts.size === 0 && posts.length > 1 && (
          <button
            onClick={selectAllPosts}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
          >
            Select All
          </button>
        )}
      </div>

      {/* Audio posts list */}
      <div className="space-y-4">
        {posts.map((post) => (
          <div
            key={post.id}
            className={`bg-white rounded-lg border p-4 transition-all ${
              selectedPosts.has(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-md'
            }`}
          >
            <div className="flex items-start gap-4">
              {/* Selection checkbox */}
              <input
                type="checkbox"
                checked={selectedPosts.has(post.id)}
                onChange={() => toggleSelectPost(post.id)}
                className="mt-1 h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />

              {/* Audio player */}
              <div className="flex-1 min-w-0">
                <div className="mb-3">
                  <AudioPlayer
                    audioUrl={post.audio_url}
                    duration={post.duration_seconds}
                    className="w-full max-w-md"
                  />
                </div>
                
                {/* Description */}
                {post.description && (
                  <p className="text-gray-700 mb-2">{post.description}</p>
                )}
                
                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-gray-500 flex-wrap">
                  {/* All reaction types */}
                  {post.audio_reactions && post.audio_reactions.length > 0 ? (() => {
                    const reactionCounts = post.audio_reactions.reduce((acc: Record<string, number>, reaction) => {
                      acc[reaction.reaction_type] = (acc[reaction.reaction_type] || 0) + 1
                      return acc
                    }, {})

                    const reactionEmojis: Record<string, string> = {
                      'like': '👍',
                      'love': '❤️',
                      'laugh': '😂',
                      'wow': '😮',
                      'sad': '😢',
                      'angry': '😠'
                    }

                    return Object.entries(reactionCounts).map(([type, count]) => (
                      <span key={type} title={`${count} ${type} reactions`}>
                        {reactionEmojis[type] || '👍'} {count}
                      </span>
                    ))
                  })() : (
                    /* Fallback to love_count if no reactions data */
                    post.love_count > 0 && <span>❤️ {post.love_count}</span>
                  )}

                  {/* Reply count with actual data */}
                  <span title={`${post.audio_replies?.length || post.reply_count} replies`}>
                    💬 {post.audio_replies?.length || post.reply_count}
                  </span>

                  {/* Play count if available */}
                  {post.play_count !== undefined && (
                    <span>▶️ {post.play_count}</span>
                  )}

                  {/* Duration and date */}
                  <span>⏱️ {post.duration_seconds.toFixed(1)}s</span>
                  <span>{formatDate(post.created_at)}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleDeleteClick(post)}
                  disabled={deletingIds.has(post.id)}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
                >
                  {deletingIds.has(post.id) ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Delete confirmation modal */}
      {postToDelete && (
        <DeleteConfirmModal
          isOpen={true}
          onClose={() => setPostToDelete(null)}
          onConfirm={confirmDelete}
          title="Delete Audio Post"
          message="Are you sure you want to delete this audio post? This action cannot be undone."
        />
      )}

      <DeleteConfirmModal {...deleteConfirm.props} />
    </div>
  )
}
