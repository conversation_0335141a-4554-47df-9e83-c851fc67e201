'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AudioRecorder } from '@/components/AudioRecorder'
import { AudioPlayer } from '@/components/AudioPlayer'
import { Heart, MessageCircle, X, Mic, Users } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface BookAudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  chapter_position: number
  page_context?: string
  love_count: number
  reply_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface BookAudioReply {
  id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

interface BookAudioDiscussionProps {
  bookId: string
  chapterId: string
  chapterTitle: string
  currentUserId?: string
  onClose: () => void
  onDiscussionAdded?: () => void
}

export function BookAudioDiscussion({
  bookId,
  chapterId,
  chapterTitle,
  currentUserId,
  onClose,
  onDiscussionAdded
}: BookAudioDiscussionProps) {
  const [posts, setPosts] = useState<BookAudioPost[]>([])
  const [loading, setLoading] = useState(true)
  const [showRecorder, setShowRecorder] = useState(false)
  const [selectedPost, setSelectedPost] = useState<BookAudioPost | null>(null)
  const [replies, setReplies] = useState<BookAudioReply[]>([])
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)
  const [recordingDescription, setRecordingDescription] = useState('')

  const supabase = createSupabaseClient()

  useEffect(() => {
    loadPosts()
  }, [bookId, chapterId])

  const loadPosts = async () => {
    try {
      const response = await fetch(`/api/books/${bookId}/audio/posts?chapterId=${chapterId}`)
      if (response.ok) {
        const data = await response.json()
        setPosts(data.posts || [])
      }
    } catch (error) {
      console.error('Error loading posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadReplies = async (postId: string) => {
    try {
      const response = await fetch(`/api/books/${bookId}/audio/replies?postId=${postId}`)
      if (response.ok) {
        const data = await response.json()
        setReplies(data.replies || [])
      }
    } catch (error) {
      console.error('Error loading replies:', error)
    }
  }

  const handleRecordingComplete = async (audioBlob: Blob, duration: number) => {
    try {
      // Upload audio file
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'post', duration })
      })

      if (!uploadResponse.ok) throw new Error('Upload failed')

      const { uploadUrl, publicUrl, key } = await uploadResponse.json()

      // Upload the audio file
      await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: { 'Content-Type': 'audio/webm' }
      })

      // Create the book audio post
      const createResponse = await fetch(`/api/books/${bookId}/audio/posts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chapterId,
          audioUrl: publicUrl,
          audioKey: key,
          duration,
          description: recordingDescription.trim() || null,
          chapterPosition: 0, // Could be enhanced to track actual position
          pageContext: chapterTitle
        })
      })

      if (createResponse.ok) {
        const { post } = await createResponse.json()
        setPosts(prev => [post, ...prev])
        setShowRecorder(false)
        setRecordingDescription('')
        // Update the parent's discussion count
        onDiscussionAdded?.()
      }
    } catch (error) {
      console.error('Error creating audio post:', error)
      alert('Failed to create audio post. Please try again.')
    }
  }

  const handleReplyComplete = async (audioBlob: Blob, duration: number) => {
    if (!selectedPost) return

    try {
      // Upload audio file
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'reply', duration })
      })

      if (!uploadResponse.ok) throw new Error('Upload failed')

      const { uploadUrl, publicUrl, key } = await uploadResponse.json()

      // Upload the audio file
      await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: { 'Content-Type': 'audio/webm' }
      })

      // Create the reply
      const createResponse = await fetch(`/api/books/${bookId}/audio/replies`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          postId: selectedPost.id,
          audioUrl: publicUrl,
          audioKey: key,
          duration
        })
      })

      if (createResponse.ok) {
        const { reply } = await createResponse.json()
        setReplies(prev => [...prev, reply])
        setShowReplyRecorder(false)
        
        // Update reply count in posts
        setPosts(prev => prev.map(post => 
          post.id === selectedPost.id 
            ? { ...post, reply_count: post.reply_count + 1 }
            : post
        ))
      }
    } catch (error) {
      console.error('Error creating reply:', error)
      alert('Failed to create reply. Please try again.')
    }
  }

  const handlePostClick = (post: BookAudioPost) => {
    setSelectedPost(post)
    loadReplies(post.id)
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-center">Loading discussions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Mic className="h-5 w-5 text-purple-600" />
              Audio Discussions
            </h2>
            <p className="text-sm text-gray-600">{chapterTitle}</p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {selectedPost ? (
            /* Reply View */
            <div className="p-4">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSelectedPost(null)}
                className="mb-4"
              >
                ← Back to discussions
              </Button>
              
              {/* Original Post */}
              <div className="bg-blue-50 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                    {selectedPost.user.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{selectedPost.user.name}</p>
                    <p className="text-xs text-gray-500">{formatTimeAgo(selectedPost.created_at)}</p>
                  </div>
                </div>
                
                {selectedPost.description && (
                  <p className="text-sm text-gray-700 mb-2">{selectedPost.description}</p>
                )}
                
                <AudioPlayer
                  audioUrl={selectedPost.audio_url}
                  duration={selectedPost.duration_seconds}
                  className="mb-2"
                />
                
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {selectedPost.love_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageCircle className="h-3 w-3" />
                    {selectedPost.reply_count}
                  </span>
                </div>
              </div>

              {/* Replies */}
              <div className="space-y-3">
                {replies.map((reply) => (
                  <div key={reply.id} className="border-l-2 border-gray-200 pl-4">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs font-medium">
                        {reply.user.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{reply.user.name}</p>
                        <p className="text-xs text-gray-500">{formatTimeAgo(reply.created_at)}</p>
                      </div>
                    </div>
                    
                    <AudioPlayer
                      audioUrl={reply.audio_url}
                      duration={reply.duration_seconds}
                      className="mb-2"
                    />
                  </div>
                ))}
              </div>

              {/* Reply Recorder */}
              {currentUserId && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  {showReplyRecorder ? (
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium mb-3">🎤 Record Reply</h4>
                      <AudioRecorder
                        maxDuration={9}
                        onRecordingComplete={handleReplyComplete}
                        onCancel={() => setShowReplyRecorder(false)}
                      />
                    </div>
                  ) : (
                    <Button 
                      onClick={() => setShowReplyRecorder(true)}
                      className="w-full"
                      variant="outline"
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Record Reply
                    </Button>
                  )}
                </div>
              )}
            </div>
          ) : (
            /* Posts List View */
            <div className="p-4">
              {/* Create New Post */}
              {currentUserId && (
                <div className="mb-6">
                  {showRecorder ? (
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h3 className="text-sm font-medium mb-3">🎤 Record Audio Discussion</h3>
                      <input
                        type="text"
                        placeholder="Optional description (50 chars max)"
                        value={recordingDescription}
                        onChange={(e) => setRecordingDescription(e.target.value.slice(0, 50))}
                        className="w-full p-2 border border-gray-300 rounded mb-3 text-sm"
                      />
                      <AudioRecorder
                        maxDuration={9}
                        onRecordingComplete={handleRecordingComplete}
                        onCancel={() => {
                          setShowRecorder(false)
                          setRecordingDescription('')
                        }}
                      />
                    </div>
                  ) : (
                    <Button 
                      onClick={() => setShowRecorder(true)}
                      className="w-full mb-4"
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Start Audio Discussion
                    </Button>
                  )}
                </div>
              )}

              {/* Posts List */}
              {posts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>No audio discussions yet for this chapter.</p>
                  {currentUserId && (
                    <p className="text-sm mt-1">Be the first to start a discussion!</p>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {posts.map((post) => (
                    <div 
                      key={post.id} 
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => handlePostClick(post)}
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                          {post.user.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className="font-medium text-sm">{post.user.name}</p>
                          <p className="text-xs text-gray-500">{formatTimeAgo(post.created_at)}</p>
                        </div>
                      </div>
                      
                      {post.description && (
                        <p className="text-sm text-gray-700 mb-2">{post.description}</p>
                      )}
                      
                      <AudioPlayer
                        audioUrl={post.audio_url}
                        duration={post.duration_seconds}
                        className="mb-2"
                      />
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {post.love_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageCircle className="h-3 w-3" />
                          {post.reply_count} replies
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
