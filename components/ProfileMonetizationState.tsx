'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface User {
  id: string
  name: string
  price_monthly?: number
  stripe_account_id?: string
}

interface ProfileMonetizationStateProps {
  user: User
  isOwnProfile: boolean
  subscriberCount?: number
}

export function ProfileMonetizationState({ 
  user, 
  isOwnProfile, 
  subscriberCount = 0 
}: ProfileMonetizationStateProps) {
  const [showSetupModal, setShowSetupModal] = useState(false)
  
  const hasMonetizationSetup = user.price_monthly && user.stripe_account_id
  
  if (isOwnProfile && !hasMonetizationSetup) {
    // Owner view - no monetization setup yet
    return (
      <Card className="border-2 border-dashed border-blue-200 bg-blue-50/30">
        <CardContent className="p-6 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">💰</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Start Earning from Your Content
          </h3>
          <p className="text-gray-600 mb-4 text-sm">
            Set up monetization to receive subscriptions and donations from your audience
          </p>
          <Button 
            onClick={() => setShowSetupModal(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Set Up Monetization
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  if (!isOwnProfile && !hasMonetizationSetup) {
    // Visitor view - creator hasn't set up monetization
    return (
      <Card className="border border-gray-200 bg-gray-50">
        <CardContent className="p-6 text-center">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
            <span className="text-lg">❤️</span>
          </div>
          <h3 className="text-base font-medium text-gray-700 mb-2">
            Support {user.name}
          </h3>
          <p className="text-gray-500 text-sm mb-4">
            This creator hasn't set up monetization yet
          </p>
          <Button 
            disabled
            className="bg-gray-300 text-gray-500 cursor-not-allowed"
          >
            Support Not Available
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  if (hasMonetizationSetup) {
    // Full monetization display
    return (
      <Card className="border border-purple-200 bg-gradient-to-br from-purple-50 to-blue-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-lg">✨</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">
                  ${(user.price_monthly! / 100).toFixed(2)}/month
                </h3>
                <p className="text-sm text-gray-600">
                  {subscriberCount} subscribers
                </p>
              </div>
            </div>
          </div>
          
          {!isOwnProfile && (
            <div className="space-y-2">
              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                Subscribe for ${(user.price_monthly! / 100).toFixed(2)}
              </Button>
              <Button variant="outline" className="w-full border-purple-200 text-purple-700 hover:bg-purple-50">
                Send Tip
              </Button>
            </div>
          )}
          
          {isOwnProfile && (
            <div className="space-y-2">
              <Button variant="outline" className="w-full">
                Manage Monetization
              </Button>
              <div className="text-center text-sm text-gray-600">
                Monthly Revenue: $XXX
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }
  
  return null
}
