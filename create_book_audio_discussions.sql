-- Book Audio Discussions Migration
-- Creates audio posts and replies tied to specific book pages/chapters

-- 1. Create book_audio_posts table (extends audio_posts for book context)
CREATE TABLE book_audio_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE NOT NULL,
    chapter_id UUID REFERENCES chapters(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL, -- R2 storage key
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL,
    waveform_data JSONB, -- For visual waveform display
    
    -- Position tracking within chapter
    chapter_position INTEGER DEFAULT 0, -- Character position or scroll position
    page_context TEXT, -- Optional context about what part of the page
    
    -- Content
    description TEXT CHECK (LENGTH(description) <= 50),
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    reply_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create book_audio_replies table
CREATE TABLE book_audio_replies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    
    -- Audio file details
    audio_url TEXT NOT NULL,
    audio_key TEXT NOT NULL, -- R2 storage key
    duration_seconds DECIMAL(3,2) CHECK (duration_seconds <= 9.0) NOT NULL,
    file_size_bytes INTEGER NOT NULL,
    
    -- Engagement
    love_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create book_audio_loves table
CREATE TABLE book_audio_loves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    book_audio_post_id UUID REFERENCES book_audio_posts(id) ON DELETE CASCADE,
    book_audio_reply_id UUID REFERENCES book_audio_replies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure user can only love a post OR reply, not both
    CHECK (
        (book_audio_post_id IS NOT NULL AND book_audio_reply_id IS NULL) OR
        (book_audio_post_id IS NULL AND book_audio_reply_id IS NOT NULL)
    ),
    -- Ensure unique love per user per post/reply
    UNIQUE(user_id, book_audio_post_id),
    UNIQUE(user_id, book_audio_reply_id)
);

-- 4. Create indexes for performance
CREATE INDEX idx_book_audio_posts_project_chapter ON book_audio_posts(project_id, chapter_id);
CREATE INDEX idx_book_audio_posts_user ON book_audio_posts(user_id);
CREATE INDEX idx_book_audio_posts_created_at ON book_audio_posts(created_at DESC);
CREATE INDEX idx_book_audio_replies_post ON book_audio_replies(book_audio_post_id);
CREATE INDEX idx_book_audio_replies_user ON book_audio_replies(user_id);

-- 5. Create triggers to update reply counts
CREATE OR REPLACE FUNCTION update_book_audio_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE book_audio_posts 
        SET reply_count = reply_count + 1 
        WHERE id = NEW.book_audio_post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE book_audio_posts 
        SET reply_count = reply_count - 1 
        WHERE id = OLD.book_audio_post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_reply_count_trigger
    AFTER INSERT OR DELETE ON book_audio_replies
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_reply_count();

-- 6. Create triggers to update love counts
CREATE OR REPLACE FUNCTION update_book_audio_love_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF NEW.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts 
            SET love_count = love_count + 1 
            WHERE id = NEW.book_audio_post_id;
        ELSIF NEW.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies 
            SET love_count = love_count + 1 
            WHERE id = NEW.book_audio_reply_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF OLD.book_audio_post_id IS NOT NULL THEN
            UPDATE book_audio_posts 
            SET love_count = love_count - 1 
            WHERE id = OLD.book_audio_post_id;
        ELSIF OLD.book_audio_reply_id IS NOT NULL THEN
            UPDATE book_audio_replies 
            SET love_count = love_count - 1 
            WHERE id = OLD.book_audio_reply_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_book_audio_love_count_trigger
    AFTER INSERT OR DELETE ON book_audio_loves
    FOR EACH ROW EXECUTE FUNCTION update_book_audio_love_count();
