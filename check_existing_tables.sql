-- Check existing tables and their structure
SELECT table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'diary_entries', 'loves', 'reactions', 'comments')
ORDER BY table_name, ordinal_position;

-- Check if audio-related tables already exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%audio%';

-- Check existing reaction/love system structure
SELECT table_name, column_name, data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND (table_name = 'loves' OR table_name = 'reactions')
ORDER BY table_name, ordinal_position;
