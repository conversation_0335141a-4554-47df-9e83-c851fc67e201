-- Set up daily cron job to update bestsellers at 8AM UTC
-- This uses pg_cron extension (needs to be enabled in Supabase)

-- Enable pg_cron extension (run this in Supabase SQL editor)
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Schedule daily bestsellers update at 8:00 AM UTC
-- This will run every day at 8 AM and update the previous day's bestsellers
SELECT cron.schedule(
    'update-daily-bestsellers',
    '0 8 * * *', -- Every day at 8:00 AM UTC
    'SELECT update_daily_bestsellers();'
);

-- Alternative: If pg_cron is not available, you can use Supabase's built-in cron
-- Create this as a webhook in Supabase Dashboard -> Edge Functions -> Cron Jobs
-- URL: https://your-project.supabase.co/functions/v1/update-daily-bestsellers
-- Schedule: 0 8 * * * (daily at 8 AM UTC)

-- View scheduled jobs
SELECT * FROM cron.job;

-- To remove the job if needed:
-- SELECT cron.unschedule('update-daily-bestsellers');
