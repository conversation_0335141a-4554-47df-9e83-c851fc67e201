-- Day 1 Badge System for First 500 Signups
-- Run this in Supabase SQL Editor BEFORE sending creator emails

-- 1. Add Day 1 badge fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS has_day1_badge BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS signup_number INTEGER;

-- 2. Add badge tier field to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS badge_tier TEXT;

-- 3. <PERSON>reate function to automatically assign tiered badges based on signup number
CREATE OR REPLACE FUNCTION assign_tiered_badges()
RETURNS TRIGGER AS $$
DECLARE
    current_signup_count INTEGER;
    badge_tier_name TEXT;
BEGIN
    -- Get current count of users (excluding the new user being inserted)
    SELECT COUNT(*) INTO current_signup_count FROM users WHERE id != NEW.id;

    -- Assign signup number (1-based)
    NEW.signup_number := current_signup_count + 1;

    -- Assign badge tier based on signup number
    IF current_signup_count < 500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'day1';
    ELSIF current_signup_count < 1000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'pioneer';
    ELSIF current_signup_count < 2500 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'founder';
    ELSIF current_signup_count < 5000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'early_adopter';
    ELSIF current_signup_count < 10000 THEN
        NEW.has_day1_badge := TRUE;
        NEW.badge_tier := 'charter_member';
    ELSE
        NEW.has_day1_badge := FALSE;
        NEW.badge_tier := NULL;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Create trigger to run the function on user signup
DROP TRIGGER IF EXISTS assign_day1_badge_trigger ON users;
DROP TRIGGER IF EXISTS assign_tiered_badges_trigger ON users;
CREATE TRIGGER assign_tiered_badges_trigger
    BEFORE INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION assign_tiered_badges();

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_signup_number ON users(signup_number);
CREATE INDEX IF NOT EXISTS idx_users_day1_badge ON users(has_day1_badge) WHERE has_day1_badge = TRUE;
CREATE INDEX IF NOT EXISTS idx_users_badge_tier ON users(badge_tier) WHERE badge_tier IS NOT NULL;

-- 6. Create function to get badge holders by tier (for admin/analytics)
CREATE OR REPLACE FUNCTION get_badge_holders_by_tier(tier_name TEXT DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    badge_tier TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    IF tier_name IS NULL THEN
        -- Return all badge holders
        RETURN QUERY
        SELECT
            u.id,
            u.name,
            u.email,
            u.signup_number,
            u.badge_tier,
            u.created_at
        FROM users u
        WHERE u.has_day1_badge = TRUE
        ORDER BY u.signup_number ASC;
    ELSE
        -- Return specific tier
        RETURN QUERY
        SELECT
            u.id,
            u.name,
            u.email,
            u.signup_number,
            u.badge_tier,
            u.created_at
        FROM users u
        WHERE u.badge_tier = tier_name
        ORDER BY u.signup_number ASC;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Legacy function for backward compatibility
CREATE OR REPLACE FUNCTION get_day1_badge_holders()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    signup_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.name,
        u.email,
        u.signup_number,
        u.created_at
    FROM users u
    WHERE u.badge_tier = 'day1'
    ORDER BY u.signup_number ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create function to get badge statistics
CREATE OR REPLACE FUNCTION get_badge_statistics()
RETURNS TABLE (
    badge_tier TEXT,
    tier_name TEXT,
    count BIGINT,
    signup_range TEXT,
    max_possible INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.badge_tier,
        CASE u.badge_tier
            WHEN 'day1' THEN 'Day 1'
            WHEN 'pioneer' THEN 'Pioneer'
            WHEN 'founder' THEN 'Founder'
            WHEN 'early_adopter' THEN 'Early Adopter'
            WHEN 'charter_member' THEN 'Charter Member'
            ELSE 'Unknown'
        END as tier_name,
        COUNT(*) as count,
        CASE u.badge_tier
            WHEN 'day1' THEN '1-500'
            WHEN 'pioneer' THEN '501-1,000'
            WHEN 'founder' THEN '1,001-2,500'
            WHEN 'early_adopter' THEN '2,501-5,000'
            WHEN 'charter_member' THEN '5,001-10,000'
            ELSE 'N/A'
        END as signup_range,
        CASE u.badge_tier
            WHEN 'day1' THEN 500
            WHEN 'pioneer' THEN 500
            WHEN 'founder' THEN 1500
            WHEN 'early_adopter' THEN 2500
            WHEN 'charter_member' THEN 5000
            ELSE 0
        END as max_possible
    FROM users u
    WHERE u.badge_tier IS NOT NULL
    GROUP BY u.badge_tier
    ORDER BY MIN(u.signup_number);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Legacy function for backward compatibility
CREATE OR REPLACE FUNCTION get_day1_badge_count()
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT COUNT(*) FROM users WHERE badge_tier = 'day1');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant permissions
GRANT EXECUTE ON FUNCTION get_badge_holders_by_tier(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_day1_badge_holders() TO authenticated;
GRANT EXECUTE ON FUNCTION get_badge_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_day1_badge_count() TO authenticated;

-- 8. Create function to reset everything for launch (USE CAREFULLY)
-- This preserves ONLY David Weaver's account and deletes all others
CREATE OR REPLACE FUNCTION reset_for_launch()
RETURNS void AS $$
BEGIN
    -- Delete ALL users except David Weaver (<EMAIL>)
    DELETE FROM users WHERE
        id != 'f296c9e6-f15d-402e-ab58-eebdc1947e1d'
        AND email != '<EMAIL>'
        AND name != 'David Weaver';

    -- Reset David Weaver's badge fields and make him signup #1 with Day 1 badge
    UPDATE users SET
        signup_number = 1,
        has_day1_badge = TRUE,
        badge_tier = 'day1'
    WHERE id = 'f296c9e6-f15d-402e-ab58-eebdc1947e1d';

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Grant permission for reset function
GRANT EXECUTE ON FUNCTION reset_for_launch() TO authenticated;

-- 10. Add helpful comments
COMMENT ON COLUMN users.has_day1_badge IS 'Permanent badge for first 500 signups - bragging rights forever';
COMMENT ON COLUMN users.signup_number IS 'Sequential signup number starting from 1';

-- 11. Test the system (optional - run to verify it works)
-- SELECT get_day1_badge_count() as current_day1_badges;
-- SELECT * FROM get_day1_badge_holders() LIMIT 10;
